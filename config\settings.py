# Configuration settings for the Discord bot

# Admin user IDs - these users have access to all commands
ADMIN_USER_IDS = [
    1246234305991147681,
    376883512671993857,
    1007550086676488212,
    972642752074502234
]

# Support user IDs - these users have access to support commands
SUPPORT_USER_IDS = [
    1274564905223192597,
    1258337015506796586
]

# Combine admin and support for easier checking
AUTHORIZED_USER_IDS = ADMIN_USER_IDS + SUPPORT_USER_IDS

# SellHub API configuration
SELLHUB_API_BASE_URL = "https://dash.sellhub.cx/api/sellhub"

# Embed colors (cyan-dark blue theme)
EMBED_COLOR = 0x00BFFF  # Deep sky blue (main theme)
EMBED_COLOR_SUCCESS = 0x00FF7F  # Spring green
EMBED_COLOR_ERROR = 0xFF4757  # Red
EMBED_COLOR_WARNING = 0xFFA502  # Orange
EMBED_COLOR_INFO = 0x3742FA  # Blue
EMBED_COLOR_PREMIUM = 0x9C88FF  # Purple
EMBED_COLOR_DARK = 0x2F3542  # Dark gray

# Database file paths
DATABASE_FILE = "data/database.json"
BACKUP_DATABASE_FILE = "data/database_backup.json"

# Role configuration (you can modify these based on your server)
BUYER_ROLE_NAME = "Buyer"
BUYER_ROLE_ID = 1334991354035437679  # The actual role ID for the buyer role

# Cooldown settings (in seconds)
COMMAND_COOLDOWN = 5
INVOICE_VERIFICATION_COOLDOWN = 60

# Maximum attempts for invoice verification
MAX_VERIFICATION_ATTEMPTS = 3
