import logging
import sys
from datetime import datetime
from typing import Optional
import discord

# ANSI color codes for colorful terminal output
class Colors:
    # Basic colors
    BLACK = '\033[30m'
    RED = '\033[31m'
    GREEN = '\033[32m'
    YELLOW = '\033[33m'
    BLUE = '\033[34m'
    MAGENTA = '\033[35m'
    CYAN = '\033[36m'
    WHITE = '\033[37m'

    # Bright colors
    BRIGHT_BLACK = '\033[90m'
    BRIGHT_RED = '\033[91m'
    BRIGHT_GREEN = '\033[92m'
    BRIGHT_YELLOW = '\033[93m'
    BRIGHT_BLUE = '\033[94m'
    BRIGHT_MAGENTA = '\033[95m'
    BRIGHT_CYAN = '\033[96m'
    BRIGHT_WHITE = '\033[97m'

    # Styles
    BOLD = '\033[1m'
    DIM = '\033[2m'
    UNDERLINE = '\033[4m'
    BLINK = '\033[5m'
    REVERSE = '\033[7m'

    # Reset
    RESET = '\033[0m'

    # Background colors
    BG_BLACK = '\033[40m'
    BG_RED = '\033[41m'
    BG_GREEN = '\033[42m'
    BG_YELLOW = '\033[43m'
    BG_BLUE = '\033[44m'
    BG_MAGENTA = '\033[45m'
    BG_CYAN = '\033[46m'
    BG_WHITE = '\033[47m'

class ColoredFormatter(logging.Formatter):
    """Custom formatter for colorful log output"""

    def __init__(self):
        super().__init__()

        # Define color schemes for different log levels
        self.colors = {
            'DEBUG': Colors.BRIGHT_BLACK,
            'INFO': Colors.BRIGHT_BLUE,
            'WARNING': Colors.BRIGHT_YELLOW,
            'ERROR': Colors.BRIGHT_RED,
            'CRITICAL': Colors.BRIGHT_MAGENTA + Colors.BOLD,
            'SUCCESS': Colors.BRIGHT_GREEN,
        }

    def format(self, record):
        # Get timestamp
        timestamp = datetime.now().strftime('%H:%M:%S')

        # Get color for log level
        color = self.colors.get(record.levelname, Colors.WHITE)

        # Format the message
        formatted_message = f"{Colors.BRIGHT_BLACK}[{timestamp}]{Colors.RESET} {color}{record.getMessage()}{Colors.RESET}"

        return formatted_message

def setup_logger(name: str = "SellHubBot", level: int = logging.INFO) -> logging.Logger:
    """Setup a colorful logger"""

    # Create logger
    logger = logging.getLogger(name)
    logger.setLevel(level)

    # Remove existing handlers to avoid duplicates
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # Create console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(level)

    # Set formatter
    formatter = ColoredFormatter()
    console_handler.setFormatter(formatter)

    # Add handler to logger
    logger.addHandler(console_handler)

    # Prevent propagation to root logger
    logger.propagate = False

    return logger

# Global logger instance
_logger: Optional[logging.Logger] = None

def get_logger() -> logging.Logger:
    """Get the global logger instance"""
    global _logger
    if _logger is None:
        _logger = setup_logger()
    return _logger

# Convenience functions for different log levels
def log_debug(message: str):
    """Log a debug message"""
    get_logger().debug(message)

def log_info(message: str):
    """Log an info message"""
    get_logger().info(message)

def log_warning(message: str):
    """Log a warning message"""
    get_logger().warning(message)

def log_error(message: str):
    """Log an error message"""
    get_logger().error(message)

def log_critical(message: str):
    """Log a critical message"""
    get_logger().critical(message)

def log_success(message: str):
    """Log a success message (custom level)"""
    # Add SUCCESS level if it doesn't exist
    if not hasattr(logging, 'SUCCESS'):
        logging.SUCCESS = 25  # Between INFO (20) and WARNING (30)
        logging.addLevelName(logging.SUCCESS, 'SUCCESS')

    logger = get_logger()
    if logger.isEnabledFor(logging.SUCCESS):
        logger._log(logging.SUCCESS, message, ())

def log_command(user: str, command: str, guild: str = None):
    """Log a command usage"""
    if guild:
        message = f"🎮 {Colors.BRIGHT_CYAN}{user}{Colors.RESET} used {Colors.BRIGHT_YELLOW}/{command}{Colors.RESET} in {Colors.BRIGHT_MAGENTA}{guild}{Colors.RESET}"
    else:
        message = f"🎮 {Colors.BRIGHT_CYAN}{user}{Colors.RESET} used {Colors.BRIGHT_YELLOW}/{command}{Colors.RESET}"
    log_info(message)

def log_verification(user: str, invoice_id: str, success: bool = True):
    """Log a verification attempt"""
    if success:
        message = f"✅ {Colors.BRIGHT_GREEN}Verification successful{Colors.RESET} for {Colors.BRIGHT_CYAN}{user}{Colors.RESET} with invoice {Colors.BRIGHT_YELLOW}{invoice_id}{Colors.RESET}"
        log_success(message)
    else:
        message = f"❌ {Colors.BRIGHT_RED}Verification failed{Colors.RESET} for {Colors.BRIGHT_CYAN}{user}{Colors.RESET} with invoice {Colors.BRIGHT_YELLOW}{invoice_id}{Colors.RESET}"
        log_error(message)

def log_api_call(endpoint: str, status_code: int, response_time: float = None):
    """Log an API call"""
    if status_code == 200:
        color = Colors.BRIGHT_GREEN
        status = "✅"
    elif status_code == 404:
        color = Colors.BRIGHT_YELLOW
        status = "⚠️"
    else:
        color = Colors.BRIGHT_RED
        status = "❌"

    time_str = f" ({response_time:.2f}ms)" if response_time else ""
    message = f"{status} API call to {Colors.BRIGHT_BLUE}{endpoint}{Colors.RESET} - {color}{status_code}{Colors.RESET}{time_str}"

    if status_code == 200:
        log_success(message)
    elif status_code == 404:
        log_warning(message)
    else:
        log_error(message)

def print_banner():
    """Print a colorful startup banner"""
    banner = f"""
{Colors.BRIGHT_CYAN}╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║  {Colors.BRIGHT_YELLOW}Discord SellHub Bot{Colors.BRIGHT_CYAN}                                      ║
║                                                              ║
║  {Colors.BRIGHT_GREEN}Features:{Colors.BRIGHT_CYAN}                                                 ║
║    • Invoice verification with SellHub API                  ║
║    • Automatic buyer role assignment                        ║
║    • Anti-abuse protection                                  ║
║    • Admin management commands                              ║
║                                                              ║
║  {Colors.BRIGHT_MAGENTA}Status: Starting up...{Colors.BRIGHT_CYAN}                                  ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝{Colors.RESET}
"""
    try:
        print(banner)
    except UnicodeEncodeError:
        # Fallback to simple banner if Unicode fails
        simple_banner = f"""
{Colors.BRIGHT_CYAN}==================================================
  Discord SellHub Bot

  Features:
    • Invoice verification with SellHub API
    • Automatic buyer role assignment
    • Anti-abuse protection
    • Admin management commands

  Status: Starting up...
=================================================={Colors.RESET}
"""
        print(simple_banner)

def print_shutdown_banner():
    """Print a colorful shutdown banner"""
    banner = f"""
{Colors.BRIGHT_RED}╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║  {Colors.BRIGHT_YELLOW}Discord SellHub Bot Shutdown{Colors.BRIGHT_RED}                             ║
║                                                              ║
║  {Colors.BRIGHT_GREEN}Thank you for using our bot!{Colors.BRIGHT_RED}                              ║
║  {Colors.BRIGHT_CYAN}All data has been saved safely.{Colors.BRIGHT_RED}                            ║
║                                                              ║
║  {Colors.BRIGHT_MAGENTA}Status: Offline{Colors.BRIGHT_RED}                                          ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝{Colors.RESET}
"""
    try:
        print(banner)
    except UnicodeEncodeError:
        # Fallback to simple banner if Unicode fails
        simple_banner = f"""
{Colors.BRIGHT_RED}==================================================
  Discord SellHub Bot Shutdown

  Thank you for using our bot!
  All data has been saved safely.

  Status: Offline
=================================================={Colors.RESET}
"""
        print(simple_banner)

async def log_user_command(bot, user: discord.User, command: str, guild: discord.Guild = None, result_data: str = None):
    """Log user command usage to a specific Discord channel"""
    try:
        from config.settings import USER_COMMAND_LOG_CHANNEL_ID, ADMIN_USER_IDS, SUPPORT_USER_IDS

        # Get the logging channel
        log_channel = bot.get_channel(USER_COMMAND_LOG_CHANNEL_ID)
        if not log_channel:
            log_error(f"❌ User command log channel {USER_COMMAND_LOG_CHANNEL_ID} not found")
            return

        # Determine user rank
        user_rank = "User"
        if user.id in ADMIN_USER_IDS:
            user_rank = "Admin"
        elif user.id in SUPPORT_USER_IDS:
            user_rank = "Support"

        # Create embed for the log
        embed = discord.Embed(
            title="📋 Command Usage Log",
            color=0x00BFFF,
            timestamp=datetime.now()
        )

        # Add user info
        embed.add_field(
            name="👤 User",
            value=f"{user.mention} ({user.name}#{user.discriminator})\nID: `{user.id}`",
            inline=True
        )

        # Add rank
        embed.add_field(
            name="🏆 Rank",
            value=user_rank,
            inline=True
        )

        # Add command
        embed.add_field(
            name="⚡ Command",
            value=f"`/{command}`",
            inline=True
        )

        # Add guild info if available
        if guild:
            embed.add_field(
                name="🏠 Server",
                value=f"{guild.name}\nID: `{guild.id}`",
                inline=False
            )

        # Add result data if available
        if result_data:
            embed.add_field(
                name="📊 Result",
                value=result_data,
                inline=False
            )

        # Set user avatar as thumbnail
        if user.avatar:
            embed.set_thumbnail(url=user.avatar.url)

        # Send to log channel
        await log_channel.send(embed=embed)

    except Exception as e:
        log_error(f"❌ Failed to log user command: {e}")
