import discord
from discord.ext import commands
from discord import app_commands
from config.settings import *
from utils.sellhub_api import SellH<PERSON>AP<PERSON>
from utils.database import Database

class InvoiceModal(discord.ui.Modal, title='Invoice Verification'):
    def __init__(self, bot, db: Database):
        super().__init__()
        self.bot = bot
        self.db = db
        self.sellhub_api = SellHubAPI()

    invoice_id = discord.ui.TextInput(
        label='Invoice ID',
        placeholder='Enter your invoice ID here...',
        required=True,
        max_length=100
    )

    async def on_submit(self, interaction: discord.Interaction):
        await interaction.response.defer(ephemeral=True)

        user_id = interaction.user.id
        invoice_id = self.invoice_id.value.strip()

        # Check if user is already verified
        if self.db.is_user_verified(user_id):
            embed = discord.Embed(
                title="❌ Already Verified",
                description="You are already verified! You can only verify one invoice per account.",
                color=EMBED_COLOR_ERROR
            )
            await interaction.followup.send(embed=embed, ephemeral=True)
            return

        # Check if invoice is already used
        if self.db.is_invoice_used(invoice_id):
            embed = discord.Embed(
                title="❌ Invoice Already Used",
                description="This invoice has already been used for verification.",
                color=EMBED_COLOR_ERROR
            )
            await interaction.followup.send(embed=embed, ephemeral=True)
            return

        # Show processing message
        processing_embed = discord.Embed(
            title="🔄 Processing...",
            description="Verifying your invoice with SellHub API. Please wait...",
            color=EMBED_COLOR
        )
        await interaction.followup.send(embed=processing_embed, ephemeral=True)

        # Validate invoice with SellHub API
        validation_result = await self.sellhub_api.validate_invoice(invoice_id)

        if not validation_result["valid"]:
            error_embed = discord.Embed(
                title="❌ Invoice Verification Failed",
                description=f"**Error:** {validation_result['error']}",
                color=EMBED_COLOR_ERROR
            )
            error_embed.add_field(
                name="What to check:",
                value="• Make sure the invoice ID is correct\n• Ensure the invoice is paid\n• Contact support if you believe this is an error",
                inline=False
            )
            await interaction.edit_original_response(embed=error_embed)
            return

        # Get email from validation result
        email = validation_result.get("email")
        if not email or email == "customer_email_not_available":
            error_embed = discord.Embed(
                title="❌ Email Not Found",
                description="Could not retrieve email from invoice. Please contact support.",
                color=EMBED_COLOR_ERROR
            )
            await interaction.edit_original_response(embed=error_embed)
            return

        # Check if email is already used
        if self.db.is_email_used(email):
            embed = discord.Embed(
                title="❌ Email Already Used",
                description="This email address has already been used for verification.",
                color=EMBED_COLOR_ERROR
            )
            await interaction.edit_original_response(embed=embed)
            return

        # Add verification to database
        discord_tag = f"{interaction.user.name}#{interaction.user.discriminator}"
        if self.db.add_verification(user_id, invoice_id, email, discord_tag):
            # Try to assign buyer role
            role_assigned = await self.assign_buyer_role(interaction.user, interaction.guild)

            success_embed = discord.Embed(
                title="✅ Verification Successful!",
                description="Your invoice has been verified successfully!",
                color=EMBED_COLOR_SUCCESS
            )
            success_embed.add_field(
                name="Invoice ID",
                value=f"`{invoice_id}`",
                inline=True
            )
            success_embed.add_field(
                name="Email",
                value=f"`{email}`",
                inline=True
            )
            success_embed.add_field(
                name="Total Amount",
                value=f"${validation_result.get('total', 'N/A')}",
                inline=True
            )

            if role_assigned:
                success_embed.add_field(
                    name="Role Assigned",
                    value=f"✅ {BUYER_ROLE_NAME} role has been assigned!",
                    inline=False
                )
            else:
                success_embed.add_field(
                    name="Role Assignment",
                    value="⚠️ Could not assign role automatically. Please contact an admin.",
                    inline=False
                )

            success_embed.set_footer(text="Thank you for your purchase!")
            await interaction.edit_original_response(embed=success_embed)
        else:
            error_embed = discord.Embed(
                title="❌ Database Error",
                description="Failed to save verification data. Please contact support.",
                color=EMBED_COLOR_ERROR
            )
            await interaction.edit_original_response(embed=error_embed)

    async def assign_buyer_role(self, user: discord.Member, guild: discord.Guild) -> bool:
        """Assign buyer role to user"""
        try:
            # Find the buyer role
            buyer_role = discord.utils.get(guild.roles, name=BUYER_ROLE_NAME)

            if not buyer_role:
                print(f"Buyer role '{BUYER_ROLE_NAME}' not found in guild {guild.name}")
                return False

            # Check if user already has the role
            if buyer_role in user.roles:
                return True

            # Assign the role
            await user.add_roles(buyer_role, reason="Invoice verification successful")
            return True

        except discord.Forbidden:
            print(f"No permission to assign roles in guild {guild.name}")
            return False
        except Exception as e:
            print(f"Error assigning buyer role: {e}")
            return False

class BuyerPanel(commands.Cog):
    def __init__(self, bot, db: Database):
        self.bot = bot
        self.db = db

    @app_commands.command(name="create_buyer_panel", description="Create a buyer verification panel")
    async def create_buyer_panel(self, interaction: discord.Interaction):
        # Check if user has permission (admin or support)
        if interaction.user.id not in AUTHORIZED_USER_IDS:
            embed = discord.Embed(
                title="❌ Access Denied",
                description="You don't have permission to use this command.",
                color=EMBED_COLOR_ERROR
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return

        # Create the main embed
        embed = discord.Embed(
            title="🛒 Buyer Role Verification",
            description="Click the button below to verify your purchase and get the Buyer role!",
            color=EMBED_COLOR
        )

        embed.add_field(
            name="📋 How it works:",
            value=(
                "1️⃣ Click the **Verify Purchase** button\n"
                "2️⃣ Enter your invoice ID from your purchase\n"
                "3️⃣ We'll verify your purchase with SellHub\n"
                "4️⃣ Get your Buyer role automatically!"
            ),
            inline=False
        )

        embed.add_field(
            name="⚠️ Important Notes:",
            value=(
                "• Each invoice can only be used once\n"
                "• Each email can only be used once\n"
                "• Each Discord account can only verify once\n"
                "• Only paid invoices are accepted"
            ),
            inline=False
        )

        embed.add_field(
            name="🆘 Need Help?",
            value="Contact our support team if you encounter any issues!",
            inline=False
        )

        embed.set_footer(text="Powered by SellHub API • Secure & Automated")
        embed.set_thumbnail(url=self.bot.user.avatar.url if self.bot.user.avatar else None)

        # Create the verification button
        view = VerificationView(self.bot, self.db)

        await interaction.response.send_message(embed=embed, view=view)

class VerificationView(discord.ui.View):
    def __init__(self, bot, db: Database):
        super().__init__(timeout=None)  # Persistent view
        self.bot = bot
        self.db = db

    @discord.ui.button(
        label="Verify Purchase",
        style=discord.ButtonStyle.primary,
        emoji="🔐",
        custom_id="verify_purchase_button"
    )
    async def verify_purchase(self, interaction: discord.Interaction, button: discord.ui.Button):
        modal = InvoiceModal(self.bot, self.db)
        await interaction.response.send_modal(modal)
