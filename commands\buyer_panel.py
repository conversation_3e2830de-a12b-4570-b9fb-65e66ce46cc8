import discord
from discord.ext import commands
from discord import app_commands
from config.settings import *
from utils.sellhub_api import SellH<PERSON><PERSON><PERSON>
from utils.database import Database
from utils.logger import log_command, log_verification, log_info, log_error, log_success, log_user_command

class InvoiceModal(discord.ui.Modal, title='Check Invoice-ID'):
    def __init__(self, bot, db: Database):
        super().__init__()
        self.bot = bot
        self.db = db
        self.sellhub_api = SellHubAPI()

    invoice_id = discord.ui.TextInput(
        label='Invoice ID',
        placeholder='Enter your invoice ID here...',
        required=True,
        max_length=100
    )

    async def on_submit(self, interaction: discord.Interaction):
        await interaction.response.defer(ephemeral=True)

        user_id = interaction.user.id
        invoice_id = self.invoice_id.value.strip()

        log_info(f"🔍 Verification attempt by {interaction.user} (ID: {user_id}) with invoice: {invoice_id}")

        # Check if user already has buyer role
        buyer_role = discord.utils.get(interaction.guild.roles, name=BUYER_ROLE_NAME)
        if buyer_role and buyer_role in interaction.user.roles:
            embed = discord.Embed(
                title="✅ Already a Buyer",
                description=f"You already have the **{BUYER_ROLE_NAME}** role!",
                color=EMBED_COLOR_SUCCESS
            )
            embed.add_field(
                name="🎉 You're all set!",
                value="No need to verify again. You already have access to buyer benefits.",
                inline=False
            )
            embed.set_thumbnail(url=interaction.user.display_avatar.url)
            embed.set_footer(text="Thank you for being a valued customer!")
            await interaction.followup.send(embed=embed, ephemeral=True)
            log_info(f"⚠️ {interaction.user} already has buyer role, skipping verification")
            return

        # Check if user is already verified in database
        if self.db.is_user_verified(user_id):
            user_data = self.db.get_user_data(user_id)
            embed = discord.Embed(
                title="❌ Already Verified",
                description="You are already verified! You can only verify one invoice per account.",
                color=EMBED_COLOR_ERROR
            )
            embed.add_field(
                name="📋 Your Verification Details",
                value=f"**Invoice ID:** `{user_data['invoice_id']}`\n**Email:** `{user_data['email']}`\n**Verified:** <t:{int(user_data['verified_at'][:10].replace('-', ''))}:R>",
                inline=False
            )
            embed.add_field(
                name="🆘 Need Help?",
                value="If you believe this is an error, please contact our support team.",
                inline=False
            )
            embed.set_thumbnail(url=interaction.user.display_avatar.url)
            await interaction.followup.send(embed=embed, ephemeral=True)
            log_info(f"⚠️ {interaction.user} already verified in database")
            return

        # Check if invoice is already used
        if self.db.is_invoice_used(invoice_id):
            embed = discord.Embed(
                title="❌ Invoice Already Used",
                description="This invoice has already been used for verification.",
                color=EMBED_COLOR_ERROR
            )
            await interaction.followup.send(embed=embed, ephemeral=True)
            return

        # Show processing message
        processing_embed = discord.Embed(
            title="🔄 Verifying Invoice...",
            description="Please wait while we check your invoice.",
            color=EMBED_COLOR_INFO
        )
        await interaction.followup.send(embed=processing_embed, ephemeral=True)

        log_info(f"🔍 Starting API validation for invoice: {invoice_id}")

        # Validate invoice with SellHub API
        validation_result = await self.sellhub_api.validate_invoice(invoice_id)

        log_info(f"📊 API validation result: {validation_result['valid']} for invoice: {invoice_id}")

        if not validation_result["valid"]:
            error_embed = discord.Embed(
                title="❌ Invoice Verification Failed",
                description=f"**Error:** {validation_result['error']}",
                color=EMBED_COLOR_ERROR
            )
            error_embed.add_field(
                name="What to check:",
                value="• Make sure the invoice ID is correct\n• Ensure the invoice is paid/completed\n• Contact support if you believe this is an error",
                inline=False
            )
            await interaction.followup.send(embed=error_embed, ephemeral=True)
            return

        # Get email from validation result
        email = validation_result.get("email")
        if not email or email == "customer_email_not_available":
            error_embed = discord.Embed(
                title="❌ Email Not Found",
                description="Could not retrieve email from invoice. Please contact support.",
                color=EMBED_COLOR_ERROR
            )
            await interaction.followup.send(embed=error_embed, ephemeral=True)
            return

        # Check if email is already used
        if self.db.is_email_used(email):
            embed = discord.Embed(
                title="❌ Email Already Used",
                description="This email address has already been used for verification.",
                color=EMBED_COLOR_ERROR
            )
            await interaction.followup.send(embed=embed, ephemeral=True)
            return

        # Add verification to database
        discord_tag = f"{interaction.user.name}#{interaction.user.discriminator}"
        if self.db.add_verification(user_id, invoice_id, email, discord_tag):
            log_success(f"✅ Database updated for user {interaction.user} with invoice {invoice_id}")

            # Try to assign buyer role
            role_assigned = await self.assign_buyer_role(interaction.user, interaction.guild)

            if role_assigned:
                log_success(f"🎭 Buyer role assigned to {interaction.user}")
            else:
                log_error(f"❌ Failed to assign buyer role to {interaction.user}")

            success_embed = discord.Embed(
                title="✅ Verification Successful!",
                description="🎉 Your invoice has been verified successfully!",
                color=EMBED_COLOR_SUCCESS
            )
            success_embed.add_field(
                name="Invoice ID",
                value=f"`{invoice_id}`",
                inline=True
            )
            success_embed.add_field(
                name="Email",
                value=f"`{email}`",
                inline=True
            )
            success_embed.add_field(
                name="Total Amount",
                value=f"${validation_result.get('total', 'N/A')}",
                inline=True
            )

            if role_assigned:
                success_embed.add_field(
                    name="Role Assigned",
                    value=f"✅ {BUYER_ROLE_NAME} role has been assigned!",
                    inline=False
                )
            else:
                success_embed.add_field(
                    name="Role Assignment",
                    value="⚠️ Could not assign role automatically. Please contact an admin.",
                    inline=False
                )

            success_embed.set_footer(text="Thank you for your purchase!")
            await interaction.followup.send(embed=success_embed, ephemeral=True)

            # Log successful verification to user command channel
            await log_user_command(
                self.bot,
                interaction.user,
                "invoice_verification",
                interaction.guild,
                f"✅ Successfully verified invoice: `{invoice_id}`\n📧 Email: `{email}`\n💰 Amount: `${validation_result.get('total', 'N/A')}`\n🎭 Role assigned: {'✅ Yes' if role_assigned else '❌ Failed'}"
            )
        else:
            error_embed = discord.Embed(
                title="❌ Database Error",
                description="Failed to save verification data. Please contact support.",
                color=EMBED_COLOR_ERROR
            )
            await interaction.followup.send(embed=error_embed, ephemeral=True)

            # Log failed verification to user command channel
            await log_user_command(
                self.bot,
                interaction.user,
                "invoice_verification",
                interaction.guild,
                f"❌ Database error during verification\n📋 Invoice: `{invoice_id}`"
            )

    async def assign_buyer_role(self, user: discord.Member, guild: discord.Guild) -> bool:
        """Assign buyer role to user"""
        try:
            # Find the buyer role by ID first, then by name as fallback
            buyer_role = guild.get_role(BUYER_ROLE_ID)
            if not buyer_role:
                # Fallback to searching by name
                buyer_role = discord.utils.get(guild.roles, name=BUYER_ROLE_NAME)
                if not buyer_role:
                    log_error(f"❌ Buyer role (ID: {BUYER_ROLE_ID} or name: '{BUYER_ROLE_NAME}') not found in {guild.name}")
                    return False
                else:
                    log_info(f"ℹ️ Found buyer role by name: {buyer_role.name} (ID: {buyer_role.id})")
            else:
                log_info(f"ℹ️ Found buyer role by ID: {buyer_role.name} (ID: {buyer_role.id})")

            # Check if user already has the role
            if buyer_role in user.roles:
                log_info(f"ℹ️ {user} already has buyer role in {guild.name}")
                return True

            # Assign the role
            await user.add_roles(buyer_role, reason="Invoice verification successful")
            log_success(f"✅ Assigned buyer role '{buyer_role.name}' to {user} in {guild.name}")
            return True

        except discord.Forbidden:
            log_error(f"❌ No permission to assign roles in {guild.name}")
            return False
        except Exception as e:
            log_error(f"❌ Error assigning buyer role: {e}")
            return False

class BuyerPanel(commands.Cog):
    def __init__(self, bot, db: Database):
        self.bot = bot
        self.db = db

    @app_commands.command(name="create_buyer_panel", description="Create a buyer verification panel")
    async def create_buyer_panel(self, interaction: discord.Interaction):
        # Check if user has permission (admin or support)
        if interaction.user.id not in AUTHORIZED_USER_IDS:
            embed = discord.Embed(
                title="❌ Access Denied",
                description="You don't have permission to use this command.",
                color=EMBED_COLOR_ERROR
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return

        log_command(str(interaction.user), "create_buyer_panel", interaction.guild.name if interaction.guild else "DM")

        # Log to user command channel
        await log_user_command(
            self.bot,
            interaction.user,
            "create_buyer_panel",
            interaction.guild,
            "✅ Buyer verification panel created successfully"
        )

        # Send confirmation message first
        confirmation_embed = discord.Embed(
            title="✅ Panel Created Successfully",
            description="The buyer verification panel has been created below!",
            color=EMBED_COLOR_SUCCESS
        )
        confirmation_embed.add_field(
            name="📊 Panel Features",
            value="• Beautiful verification interface\n• Real-time SellHub API integration\n• Anti-abuse protection\n• Automatic role assignment",
            inline=False
        )
        confirmation_embed.set_footer(text=f"Created by {interaction.user.display_name}")

        await interaction.response.send_message(embed=confirmation_embed, ephemeral=True)

        # Create the main verification embed with enhanced design
        embed = discord.Embed(
            title="🛒 Divine Services - Digital Software Distributor",
            description="Enjoy your favorite digital products with the ease of instant payments & delivery!",
            color=EMBED_COLOR
        )



        # Add verification instructions
        embed.add_field(
            name="📋 How to Verify:",
            value=(
                "1️⃣ Click **Check Invoice-ID** below\n"
                "2️⃣ Enter your invoice ID\n"
                "3️⃣ Get your Buyer role automatically!"
            ),
            inline=False
        )

        # Add benefits section
        embed.add_field(
            name="🎁 Buyer Role Benefits:",
            value="Buyer role gives access to vector files",
            inline=False
        )

        # Set thumbnail to bot avatar or a default icon
        if self.bot.user.avatar:
            embed.set_thumbnail(url=self.bot.user.avatar.url)

        # Set footer
        embed.set_footer(
            text="Powered by SellHub API • Secure & Automated Verification",
            icon_url=self.bot.user.avatar.url if self.bot.user.avatar else None
        )

        # Create the verification view with buttons
        view = VerificationView(self.bot, self.db)

        # Send the main panel (not as a reply)
        channel = interaction.channel
        await channel.send(embed=embed, view=view)

        log_success(f"✅ Buyer panel created by {interaction.user} in {interaction.guild.name if interaction.guild else 'DM'}")

class VerificationView(discord.ui.View):
    def __init__(self, bot, db: Database):
        super().__init__(timeout=None)  # Persistent view
        self.bot = bot
        self.db = db

        # Add the link button manually
        link_button = discord.ui.Button(
            label="Official Store",
            style=discord.ButtonStyle.link,
            emoji="🏪",
            url="https://divine.land"
        )
        self.add_item(link_button)

    @discord.ui.button(
        label="Check Invoice-ID",
        style=discord.ButtonStyle.success,
        emoji="🔍",
        custom_id="verify_purchase_button"
    )
    async def verify_purchase(self, interaction: discord.Interaction, button: discord.ui.Button):
        log_info(f"🔐 {interaction.user} clicked verify button")
        modal = InvoiceModal(self.bot, self.db)
        await interaction.response.send_modal(modal)


