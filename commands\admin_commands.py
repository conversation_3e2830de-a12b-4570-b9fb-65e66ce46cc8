import discord
from discord.ext import commands
from discord import app_commands
from typing import Optional
import re
from config.settings import *
from utils.database import Database
from utils.sellhub_api import SellHubAP<PERSON>
from utils.logger import log_command, log_info, log_error, log_success, log_warning

class AdminCommands(commands.Cog):
    def __init__(self, bot, db: Database):
        self.bot = bot
        self.db = db
        self.api = SellHubAPI()

    def is_admin(self, user_id: int) -> bool:
        """Check if user is an admin"""
        return user_id in ADMIN_USER_IDS

    def is_authorized(self, user_id: int) -> bool:
        """Check if user is authorized (admin or support)"""
        return user_id in AUTHORIZED_USER_IDS

    @app_commands.command(name="givebuyer", description="Give buyer role to a user (Admin only)")
    @app_commands.describe(user="The user to give the buyer role to")
    async def give_buyer(self, interaction: discord.Interaction, user: discord.Member):
        if not self.is_admin(interaction.user.id):
            embed = discord.Embed(
                title="❌ Access Denied",
                description="Only administrators can use this command.",
                color=EMBED_COLOR_ERROR
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return

        log_command(str(interaction.user), "givebuyer", interaction.guild.name if interaction.guild else "DM")

        # Find the buyer role
        buyer_role = discord.utils.get(interaction.guild.roles, name=BUYER_ROLE_NAME)

        if not buyer_role:
            embed = discord.Embed(
                title="❌ Role Not Found",
                description=f"The '{BUYER_ROLE_NAME}' role doesn't exist in this server.",
                color=EMBED_COLOR_ERROR
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return

        # Check if user already has the role
        if buyer_role in user.roles:
            embed = discord.Embed(
                title="⚠️ Already Has Role",
                description=f"{user.mention} already has the {BUYER_ROLE_NAME} role.",
                color=EMBED_COLOR_WARNING
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return

        try:
            await user.add_roles(buyer_role, reason=f"Manual assignment by {interaction.user}")

            embed = discord.Embed(
                title="✅ Role Assigned",
                description=f"Successfully gave {BUYER_ROLE_NAME} role to {user.mention}",
                color=EMBED_COLOR_SUCCESS
            )
            embed.add_field(name="Assigned by", value=interaction.user.mention, inline=True)
            embed.add_field(name="User", value=user.mention, inline=True)

            await interaction.response.send_message(embed=embed)

        except discord.Forbidden:
            embed = discord.Embed(
                title="❌ Permission Error",
                description="I don't have permission to assign roles.",
                color=EMBED_COLOR_ERROR
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)

    @app_commands.command(name="unlink", description="Remove a user's verification (Admin only)")
    @app_commands.describe(user="The user to unlink")
    async def unlink_user(self, interaction: discord.Interaction, user: discord.Member):
        if not self.is_admin(interaction.user.id):
            embed = discord.Embed(
                title="❌ Access Denied",
                description="Only administrators can use this command.",
                color=EMBED_COLOR_ERROR
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return

        # Check if user is verified
        user_data = self.db.get_user_data(user.id)
        if not user_data:
            embed = discord.Embed(
                title="❌ User Not Found",
                description=f"{user.mention} is not verified in the database.",
                color=EMBED_COLOR_ERROR
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return

        # Remove verification
        if self.db.remove_user_verification(user.id):
            # Try to remove buyer role
            buyer_role = discord.utils.get(interaction.guild.roles, name=BUYER_ROLE_NAME)
            role_removed = False

            if buyer_role and buyer_role in user.roles:
                try:
                    await user.remove_roles(buyer_role, reason=f"Unlinked by {interaction.user}")
                    role_removed = True
                except discord.Forbidden:
                    pass

            embed = discord.Embed(
                title="✅ User Unlinked",
                description=f"Successfully removed verification for {user.mention}",
                color=EMBED_COLOR_SUCCESS
            )
            embed.add_field(name="Previous Invoice", value=f"`{user_data['invoice_id']}`", inline=True)
            embed.add_field(name="Previous Email", value=f"`{user_data['email']}`", inline=True)
            embed.add_field(name="Role Removed", value="✅ Yes" if role_removed else "❌ No", inline=True)
            embed.add_field(name="Unlinked by", value=interaction.user.mention, inline=False)

            await interaction.response.send_message(embed=embed)
        else:
            embed = discord.Embed(
                title="❌ Database Error",
                description="Failed to remove user verification from database.",
                color=EMBED_COLOR_ERROR
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)

    @app_commands.command(name="link", description="Manually link a user with invoice (Admin only)")
    @app_commands.describe(
        user="The user to link",
        invoice_id="The invoice ID to link",
        email="The email to link (optional)"
    )
    async def link_user(self, interaction: discord.Interaction, user: discord.Member, invoice_id: str, email: Optional[str] = None):
        if not self.is_admin(interaction.user.id):
            embed = discord.Embed(
                title="❌ Access Denied",
                description="Only administrators can use this command.",
                color=EMBED_COLOR_ERROR
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return

        await interaction.response.defer()

        # Check if user is already verified
        if self.db.is_user_verified(user.id):
            embed = discord.Embed(
                title="❌ User Already Verified",
                description=f"{user.mention} is already verified.",
                color=EMBED_COLOR_ERROR
            )
            await interaction.followup.send(embed=embed, ephemeral=True)
            return

        # Check if invoice is already used
        if self.db.is_invoice_used(invoice_id):
            embed = discord.Embed(
                title="❌ Invoice Already Used",
                description=f"Invoice `{invoice_id}` is already linked to another user.",
                color=EMBED_COLOR_ERROR
            )
            await interaction.followup.send(embed=embed, ephemeral=True)
            return

        # If no email provided, try to get it from SellHub API
        if not email:
            validation_result = await self.sellhub_api.validate_invoice(invoice_id)
            if validation_result["valid"]:
                email = validation_result.get("email")

            if not email:
                embed = discord.Embed(
                    title="❌ Email Required",
                    description="Could not retrieve email from invoice. Please provide an email manually.",
                    color=EMBED_COLOR_ERROR
                )
                await interaction.followup.send(embed=embed, ephemeral=True)
                return

        # Check if email is already used
        if self.db.is_email_used(email):
            embed = discord.Embed(
                title="❌ Email Already Used",
                description=f"Email `{email}` is already linked to another user.",
                color=EMBED_COLOR_ERROR
            )
            await interaction.followup.send(embed=embed, ephemeral=True)
            return

        # Add verification
        discord_tag = f"{user.name}#{user.discriminator}"
        if self.db.add_verification(user.id, invoice_id, email, discord_tag):
            # Try to assign buyer role
            buyer_role = discord.utils.get(interaction.guild.roles, name=BUYER_ROLE_NAME)
            role_assigned = False

            if buyer_role and buyer_role not in user.roles:
                try:
                    await user.add_roles(buyer_role, reason=f"Manual link by {interaction.user}")
                    role_assigned = True
                except discord.Forbidden:
                    pass

            embed = discord.Embed(
                title="✅ User Linked Successfully",
                description=f"Successfully linked {user.mention} with invoice verification.",
                color=EMBED_COLOR_SUCCESS
            )
            embed.add_field(name="User", value=user.mention, inline=True)
            embed.add_field(name="Invoice ID", value=f"`{invoice_id}`", inline=True)
            embed.add_field(name="Email", value=f"`{email}`", inline=True)
            embed.add_field(name="Role Assigned", value="✅ Yes" if role_assigned else "❌ No", inline=True)
            embed.add_field(name="Linked by", value=interaction.user.mention, inline=True)

            await interaction.followup.send(embed=embed)
        else:
            embed = discord.Embed(
                title="❌ Database Error",
                description="Failed to save verification data.",
                color=EMBED_COLOR_ERROR
            )
            await interaction.followup.send(embed=embed, ephemeral=True)

    @app_commands.command(name="cleardb", description="Clear all verification data (Admin only)")
    async def clear_database(self, interaction: discord.Interaction):
        if not self.is_admin(interaction.user.id):
            embed = discord.Embed(
                title="❌ Access Denied",
                description="Only administrators can use this command.",
                color=EMBED_COLOR_ERROR
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return

        # Get current stats before clearing
        stats = self.db.get_stats()

        # Create confirmation view
        view = ClearDatabaseView(self.db, stats)

        embed = discord.Embed(
            title="⚠️ Clear Database Confirmation",
            description="**This action will permanently delete ALL verification data!**",
            color=EMBED_COLOR_WARNING
        )
        embed.add_field(name="Current Data", value=f"• {stats['total_users']} verified users\n• {stats['total_invoices']} invoices\n• {stats['total_emails']} emails", inline=False)
        embed.add_field(name="⚠️ Warning", value="This action cannot be undone!", inline=False)

        await interaction.response.send_message(embed=embed, view=view, ephemeral=True)

    @app_commands.command(name="stats", description="Show verification statistics (Admin/Support)")
    async def show_stats(self, interaction: discord.Interaction):
        if not self.is_authorized(interaction.user.id):
            embed = discord.Embed(
                title="❌ Access Denied",
                description="Only administrators and support can use this command.",
                color=EMBED_COLOR_ERROR
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return

        stats = self.db.get_stats()

        embed = discord.Embed(
            title="📊 Verification Statistics",
            description="Current database statistics",
            color=EMBED_COLOR
        )

        embed.add_field(
            name="👥 Users",
            value=f"**{stats['total_users']}** verified users",
            inline=True
        )
        embed.add_field(
            name="🧾 Invoices",
            value=f"**{stats['total_invoices']}** invoices used",
            inline=True
        )
        embed.add_field(
            name="📧 Emails",
            value=f"**{stats['total_emails']}** emails registered",
            inline=True
        )
        embed.add_field(
            name="🔢 Total Verifications",
            value=f"**{stats['total_verifications']}** all-time",
            inline=True
        )

        if stats['created_at']:
            try:
                from datetime import datetime
                created_dt = datetime.fromisoformat(stats['created_at'][:19])
                created_timestamp = int(created_dt.timestamp())
                embed.add_field(
                    name="📅 Database Created",
                    value=f"<t:{created_timestamp}:D>",
                    inline=True
                )
            except:
                embed.add_field(
                    name="📅 Database Created",
                    value=stats['created_at'][:19],
                    inline=True
                )

        if stats['last_backup']:
            try:
                from datetime import datetime
                backup_dt = datetime.fromisoformat(stats['last_backup'][:19])
                backup_timestamp = int(backup_dt.timestamp())
                embed.add_field(
                    name="💾 Last Backup",
                    value=f"<t:{backup_timestamp}:R>",
                    inline=True
                )
            except:
                embed.add_field(
                    name="💾 Last Backup",
                    value=stats['last_backup'][:19],
                    inline=True
                )

        embed.set_footer(text=f"Requested by {interaction.user.display_name}")

        await interaction.response.send_message(embed=embed, ephemeral=True)

    @app_commands.command(name="get_invoice_info", description="Get detailed invoice information (Admin/Support)")
    @app_commands.describe(invoice_id="The invoice ID to look up")
    async def get_invoice_info(self, interaction: discord.Interaction, invoice_id: str):
        if not self.is_authorized(interaction.user.id):
            embed = discord.Embed(
                title="❌ Access Denied",
                description="Only administrators and support can use this command.",
                color=EMBED_COLOR_ERROR
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return

        await interaction.response.defer(ephemeral=True)

        # Log the command usage
        log_command(f"{interaction.user.name}#{interaction.user.discriminator}", f"get_invoice_info {invoice_id}", interaction.guild.name if interaction.guild else "DM")

        try:
            # Get invoice info from SellHub API
            invoice_data = await self.api.get_invoice_info(invoice_id)

            if not invoice_data:
                embed = discord.Embed(
                    title="❌ Invoice Not Found",
                    description=f"No invoice found with ID: `{invoice_id}`",
                    color=EMBED_COLOR_ERROR
                )
                await interaction.followup.send(embed=embed, ephemeral=True)
                return

            # Check if invoice is linked to a Discord user
            db_invoice_data = None
            linked_user = None
            if self.db.is_invoice_used(invoice_id):
                db_invoice_data = self.db.data["invoices"].get(invoice_id)
                if db_invoice_data:
                    user_id = db_invoice_data.get("user_id")
                    if user_id:
                        try:
                            linked_user = await interaction.client.fetch_user(user_id)
                        except:
                            linked_user = None

            # Create detailed embed
            embed = discord.Embed(
                title="📋 Invoice Information",
                description=f"**Invoice ID:** `{invoice_id}`",
                color=EMBED_COLOR_INFO
            )

            # Basic invoice info
            total_usd = invoice_data.get('totalInUsd', 'N/A')
            status = invoice_data.get('status', 'N/A')
            embed.add_field(
                name="💰 Payment Details",
                value=f"**Amount:** ${total_usd}\n**Status:** {status}\n**Currency:** USD",
                inline=True
            )

            # Customer info - check discord account first, then customer
            discord_account = invoice_data.get('discordAccount')
            customer_email = 'N/A'
            customer_name = 'N/A'

            if discord_account:
                customer_email = discord_account.get('email', 'N/A')
                customer_name = discord_account.get('username', 'N/A')
            else:
                # Try to get customer info if no discord account
                customer_id = invoice_data.get('customerId')
                if customer_id:
                    try:
                        customer_data = await self.api.get_customer(customer_id)
                        if customer_data:
                            customer_email = customer_data.get('email', 'N/A')
                            customer_name = customer_data.get('name', customer_data.get('firstName', 'N/A'))
                    except:
                        pass

            # If still no email, check if there's a direct email field
            if customer_email == 'N/A':
                customer_email = invoice_data.get('email', 'N/A')

            embed.add_field(
                name="👤 Customer Info",
                value=f"**Email:** `{customer_email}`\n**Name:** {customer_name}",
                inline=True
            )

            # Payment method - use correct field names from debug
            payment_method = invoice_data.get('paymentMethod', 'N/A')

            # Gateway info - only show if enabled in settings
            if SHOW_GATEWAY_INVOICE_INFO:
                displayed_payment_info = invoice_data.get('displayedPaymentInfo', '')
                gateway = displayed_payment_info if displayed_payment_info else 'N/A'
                payment_value = f"**Method:** {payment_method}\n**Gateway:** {gateway}"
            else:
                payment_value = f"**Method:** {payment_method}"

            embed.add_field(
                name="💳 Payment Method",
                value=payment_value,
                inline=True
            )

            # Product info - extract clean product information
            delivered_product = 'N/A'
            product_type = 'Digital Product'

            # Check invoiceFields for product variant information
            invoice_fields = invoice_data.get('invoiceFields')
            if invoice_fields and isinstance(invoice_fields, list) and len(invoice_fields) > 0:
                field_data = invoice_fields[0]
                if isinstance(field_data, dict):
                    # Extract productVariantId cleanly
                    variant_id = field_data.get('productVariantId')
                    if variant_id:
                        # Clean up the variant ID for display
                        clean_id = variant_id.replace('-', ' ').title()
                        delivered_product = f"Product: {clean_id}"
                    else:
                        delivered_product = "Digital Product"
                else:
                    # Handle string format with regex
                    field_str = str(field_data)
                    variant_match = re.search(r"'productVariantId':\s*'([^']+)'", field_str)
                    if variant_match:
                        variant_id = variant_match.group(1)
                        # Clean up the variant ID for display
                        clean_id = variant_id.replace('-', ' ').title()
                        delivered_product = f"Product: {clean_id}"
                    else:
                        delivered_product = "Digital Product"

            # Check invoiceItems as fallback
            if delivered_product == 'N/A':
                invoice_items = invoice_data.get('invoiceItems')
                if invoice_items and isinstance(invoice_items, list) and len(invoice_items) > 0:
                    first_item = invoice_items[0]
                    if isinstance(first_item, dict):
                        delivered_product = (first_item.get('name') or
                                           first_item.get('product_name') or
                                           first_item.get('description') or
                                           "Digital Product")

            # Final fallback
            if delivered_product == 'N/A':
                delivered_product = "Digital Product"

            embed.add_field(
                name="📦 Product Details",
                value=f"**Type:** {product_type}\n**Delivered:** {delivered_product}",
                inline=True
            )

            # Temporary debug: Show raw invoice fields to find the actual product name
            if invoice_fields:
                debug_info = str(invoice_fields)[:200] + "..." if len(str(invoice_fields)) > 200 else str(invoice_fields)
                embed.add_field(
                    name="🔍 Debug: Invoice Fields",
                    value=f"```{debug_info}```",
                    inline=False
                )

            # Delivery info - check for fulfillment status
            # Since status is 'completed', delivery is likely completed too
            delivery_status = "Completed" if status == "completed" else "Pending"
            delivery_method = "Digital" if payment_method == "cardToCrypto" else "N/A"
            embed.add_field(
                name="🚚 Delivery Info",
                value=f"**Status:** {delivery_status}\n**Method:** {delivery_method}",
                inline=True
            )

            # Timestamps - use correct field names from debug
            created_at = invoice_data.get('createdAt', 'N/A')
            # For paid timestamp, check if status is completed and use createdAt or look for other fields
            paid_at = 'N/A'
            if status == 'completed':
                # If completed, it was likely paid around creation time
                paid_at = created_at

            # Format timestamps
            created_display = created_at[:19] if created_at != 'N/A' and created_at else 'N/A'
            paid_display = paid_at[:19] if paid_at != 'N/A' and paid_at else 'N/A'

            embed.add_field(
                name="⏰ Timestamps",
                value=f"**Created:** {created_display}\n**Paid:** {paid_display}",
                inline=True
            )

            # Discord verification info
            if db_invoice_data and linked_user:
                embed.add_field(
                    name="🔗 Discord Verification",
                    value=f"**Linked User:** {linked_user.mention}\n**User ID:** `{linked_user.id}`\n**Verified At:** {db_invoice_data.get('verified_at', 'N/A')[:19]}",
                    inline=False
                )
            else:
                embed.add_field(
                    name="🔗 Discord Verification",
                    value="❌ Not linked to any Discord account",
                    inline=False
                )

            # Additional details if available
            notes = (invoice_data.get('notes') or
                    invoice_data.get('description') or
                    invoice_data.get('memo'))
            if notes:
                embed.add_field(
                    name="📝 Notes",
                    value=notes,
                    inline=False
                )

            embed.set_footer(text=f"Requested by {interaction.user.display_name}")

            await interaction.followup.send(embed=embed, ephemeral=True)

            # Log to admin channel
            if LOG_CHANNEL_ID:
                try:
                    log_channel = interaction.client.get_channel(LOG_CHANNEL_ID)
                    if log_channel:
                        log_embed = discord.Embed(
                            title="📋 Invoice Info Command Used",
                            color=EMBED_COLOR_INFO
                        )
                        log_embed.add_field(name="👤 User", value=f"{interaction.user.mention} ({interaction.user.id})", inline=True)
                        log_embed.add_field(name="📋 Invoice ID", value=f"`{invoice_id}`", inline=True)
                        log_embed.add_field(name="🏠 Guild", value=interaction.guild.name if interaction.guild else "DM", inline=True)
                        log_embed.add_field(name="💰 Amount", value=f"${invoice_data.get('totalInUsd', 'N/A')}", inline=True)
                        log_embed.add_field(name="📧 Customer", value=f"`{customer_email}`", inline=True)
                        log_embed.add_field(name="🔗 Discord Link", value="✅ Yes" if linked_user else "❌ No", inline=True)
                        log_embed.timestamp = discord.utils.utcnow()
                        await log_channel.send(embed=log_embed)
                except Exception as e:
                    log_error(f"Failed to log to channel: {e}")

        except Exception as e:
            log_error(f"Error getting invoice info: {e}")
            embed = discord.Embed(
                title="❌ API Error",
                description="Failed to retrieve invoice information. Please try again later.",
                color=EMBED_COLOR_ERROR
            )
            await interaction.followup.send(embed=embed, ephemeral=True)

    @app_commands.command(name="checkuser", description="Check a user's verification status (Admin/Support)")
    @app_commands.describe(user="The user to check")
    async def check_user(self, interaction: discord.Interaction, user: discord.Member):
        if not self.is_authorized(interaction.user.id):
            embed = discord.Embed(
                title="❌ Access Denied",
                description="Only administrators and support can use this command.",
                color=EMBED_COLOR_ERROR
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return

        user_data = self.db.get_user_data(user.id)

        if not user_data:
            embed = discord.Embed(
                title="❌ User Not Verified",
                description=f"{user.mention} is not verified in the database.",
                color=EMBED_COLOR_ERROR
            )
            embed.add_field(name="User ID", value=f"`{user.id}`", inline=True)
            embed.add_field(name="Username", value=f"`{user.name}#{user.discriminator}`", inline=True)
        else:
            embed = discord.Embed(
                title="✅ User Verified",
                description=f"{user.mention} is verified in the database.",
                color=EMBED_COLOR_SUCCESS
            )
            embed.add_field(name="Invoice ID", value=f"`{user_data['invoice_id']}`", inline=True)
            embed.add_field(name="Email", value=f"`{user_data['email']}`", inline=True)
            embed.add_field(name="Verified At", value=f"`{user_data['verified_at'][:19]}`", inline=True)
            embed.add_field(name="Discord Tag", value=f"`{user_data['discord_tag']}`", inline=True)

            # Check if user has buyer role
            buyer_role = discord.utils.get(interaction.guild.roles, name=BUYER_ROLE_NAME)
            has_role = buyer_role and buyer_role in user.roles
            embed.add_field(name="Has Buyer Role", value="✅ Yes" if has_role else "❌ No", inline=True)

        embed.set_footer(text=f"Checked by {interaction.user.display_name}")

        await interaction.response.send_message(embed=embed, ephemeral=True)

class ClearDatabaseView(discord.ui.View):
    def __init__(self, db: Database, stats: dict):
        super().__init__(timeout=60)
        self.db = db
        self.stats = stats

    @discord.ui.button(label="Confirm Clear", style=discord.ButtonStyle.danger, emoji="🗑️")
    async def confirm_clear(self, interaction: discord.Interaction, button: discord.ui.Button):
        if self.db.clear_database():
            embed = discord.Embed(
                title="✅ Database Cleared",
                description="All verification data has been successfully cleared.",
                color=EMBED_COLOR_SUCCESS
            )
            embed.add_field(name="Data Removed", value=f"• {self.stats['total_users']} verified users\n• {self.stats['total_invoices']} invoices\n• {self.stats['total_emails']} emails", inline=False)
            embed.add_field(name="Cleared by", value=interaction.user.mention, inline=False)
        else:
            embed = discord.Embed(
                title="❌ Clear Failed",
                description="Failed to clear the database.",
                color=EMBED_COLOR_ERROR
            )

        # Disable all buttons
        for item in self.children:
            item.disabled = True

        await interaction.response.edit_message(embed=embed, view=self)

    @discord.ui.button(label="Cancel", style=discord.ButtonStyle.secondary, emoji="❌")
    async def cancel_clear(self, interaction: discord.Interaction, button: discord.ui.Button):
        embed = discord.Embed(
            title="❌ Operation Cancelled",
            description="Database clear operation has been cancelled.",
            color=EMBED_COLOR
        )

        # Disable all buttons
        for item in self.children:
            item.disabled = True

        await interaction.response.edit_message(embed=embed, view=self)