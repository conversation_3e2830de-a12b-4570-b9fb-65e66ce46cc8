# Follow these insturctions while making the bot.
I want a discord bot, that utlizes the sellhub API [https://docs.sellhub.cx/api]. I want a get buyer role in discord, please make a (a slash command) /create_buyer_panel command. Make a robust cyan-dark-ish blue embed, telling the user to input their invoice-id. Also, make sure no one abuses and spams the same invoice. Make sure it's not from the same email, and invoice. Make it linked to 1 user. Add admin commands like /unlink, /cleardb, /link, /givebuyer (gives a user the buyer role). Allowing only admins. Role USER id'(s): 1246234305991147681, 376883512671993857, 1007550086676488212, 972642752074502234. Make them always have access to every command. These are the support USER id'(s): 1274564905223192597, 1258337015506796586. Save everything in a .json, organize and optmize the code.
# What I have put in for you.
I have put in .env, where my bot token, and sellhub api token is stored. And use index.py, you can make seperate folders to store seperate modules.