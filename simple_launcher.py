#!/usr/bin/env python3
"""
Simple Multi-Bot Launcher
Starts multiple Discord bots and displays their console output.
"""

# ============================================================================
# BOT CONFIGURATION
# ============================================================================

START_BOT_RUNS = [
    "start.py",
    # "ticket_bot.py"
]

# ============================================================================

import subprocess
import sys
import os
import threading
import time
import signal

class BotRunner:
    def __init__(self, script_name):
        self.script_name = script_name
        self.bot_name = os.path.splitext(script_name)[0].upper()
        self.process = None
        self.running = False

    def start(self):
        """Start the bot process"""
        try:
            self.process = subprocess.Popen(
                [sys.executable, self.script_name],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            self.running = True

            # Start log reader thread
            log_thread = threading.Thread(target=self._read_logs, daemon=True)
            log_thread.start()

            print(f"✅ Started: {self.bot_name}")
            return True

        except Exception as e:
            print(f"❌ Failed to start {self.bot_name}: {e}")
            return False

    def _read_logs(self):
        """Read and display logs from the bot process"""
        try:
            for line in iter(self.process.stdout.readline, ''):
                if line:
                    # Display with bot prefix
                    clean_line = line.rstrip('\n\r')
                    if clean_line:
                        print(f"[{self.bot_name}] {clean_line}")

                # Check if process is still running
                if self.process.poll() is not None:
                    break

        except Exception as e:
            print(f"[{self.bot_name}] ❌ Log reader error: {e}")
        finally:
            self.running = False
            if self.process and self.process.poll() is not None:
                exit_code = self.process.poll()
                if exit_code != 0:
                    print(f"[{self.bot_name}] 💥 Bot crashed with exit code {exit_code}")
                else:
                    print(f"[{self.bot_name}] ✅ Bot exited normally")

    def stop(self):
        """Stop the bot process"""
        if self.process and self.running:
            try:
                self.process.terminate()
                self.process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.process.kill()
                self.process.wait()
            except Exception as e:
                print(f"[{self.bot_name}] ❌ Error stopping: {e}")

            self.running = False
            print(f"[{self.bot_name}] 🛑 Bot stopped")

class MultiBotLauncher:
    def __init__(self):
        self.bots = []
        self.running = False

        # Set up signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def start_all(self):
        """Start all configured bots"""
        print("🚀 Multi-Bot Launcher")
        print("=" * 50)

        if not START_BOT_RUNS:
            print("❌ No bots configured in START_BOT_RUNS")
            return

        # Check if scripts exist
        for script in START_BOT_RUNS:
            if not os.path.exists(script):
                print(f"❌ Script not found: {script}")
                return

        print(f"🚀 Starting {len(START_BOT_RUNS)} bot(s)...")
        print()

        # Start all bots
        for script in START_BOT_RUNS:
            bot = BotRunner(script)
            if bot.start():
                self.bots.append(bot)
            time.sleep(1)  # Small delay between starts

        if not self.bots:
            print("❌ No bots started successfully")
            return

        print()
        print(f"📊 {len(self.bots)} bot(s) running. Press Ctrl+C to stop all bots.")
        print("=" * 50)
        print()

        self.running = True

        # Keep the main thread alive
        try:
            while self.running and any(bot.running for bot in self.bots):
                time.sleep(1)
        except KeyboardInterrupt:
            pass

    def stop_all(self):
        """Stop all running bots"""
        print()
        print("🛑 Stopping all bots...")

        self.running = False

        for bot in self.bots:
            if bot.running:
                bot.stop()

        print("✅ All bots stopped")

    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        print()
        print("🛑 Received shutdown signal")
        self.stop_all()
        sys.exit(0)

def main():
    """Main function"""
    launcher = MultiBotLauncher()

    try:
        launcher.start_all()
    except KeyboardInterrupt:
        pass
    finally:
        launcher.stop_all()

if __name__ == "__main__":
    main()
