# Discord SellHub Buyer Role Bot

A robust Discord bot that integrates with the SellHub API to automatically verify purchases and assign buyer roles to users.

## Features

- **Automatic Invoice Verification**: Verifies invoices through SellHub API
- **Anti-Abuse System**: Prevents duplicate invoices, emails, and user verifications
- **Role Management**: Automatically assigns buyer roles to verified users
- **Admin Commands**: Comprehensive admin tools for managing verifications
- **Persistent Data**: JSON-based database with automatic backups
- **Beautiful Embeds**: Cyan-dark blue themed embeds for better UX

## Commands

### User Commands
- `/create_buyer_panel` - Creates a verification panel (Admin/Support only)

### Admin Commands (Admin only)
- `/givebuyer <user>` - Manually assign buyer role to a user
- `/unlink <user>` - Remove a user's verification and role
- `/link <user> <invoice_id> [email]` - Manually link a user with an invoice
- `/cleardb` - Clear all verification data (with confirmation)

### Admin/Support Commands
- `/stats` - Show verification statistics
- `/checkuser <user>` - Check a user's verification status
- `/get_invoice_info <invoice_id>` - Get detailed invoice information from SellHub API

## Setup Instructions

### 1. Prerequisites
- Python 3.8 or higher
- Discord bot token
- SellHub API token

### 2. Installation

1. Clone or download this repository
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Create a `.env` file based on `.env.example`:
   ```env
   BOT_TOKEN=your_discord_bot_token_here
   SELLHUB_API_TOKEN=your_sellhub_api_token_here
   ```

### 3. Discord Bot Setup

1. Go to [Discord Developer Portal](https://discord.com/developers/applications)
2. Create a new application and bot
3. Copy the bot token to your `.env` file
4. Enable the following bot permissions:
   - Send Messages
   - Use Slash Commands
   - Manage Roles
   - Read Message History
   - Embed Links

5. Invite the bot to your server with the required permissions

### 4. Server Setup

1. Create a role named "Buyer" (or modify `BUYER_ROLE_NAME` in `config/settings.py`)
2. Ensure the bot's role is higher than the "Buyer" role in the role hierarchy
3. Update the user IDs in `config/settings.py`:
   - `ADMIN_USER_IDS`: Users with full admin access
   - `SUPPORT_USER_IDS`: Users with support access

### 5. Running the Bot

```bash
python index.py
```

## Configuration

Edit `config/settings.py` to customize:

- **User IDs**: Admin and support user IDs
- **Role Names**: Buyer role name
- **Colors**: Embed colors
- **Cooldowns**: Command cooldowns
- **API Settings**: SellHub API configuration

## File Structure

```
├── index.py                 # Main bot file
├── config/
│   ├── __init__.py
│   └── settings.py          # Configuration settings
├── commands/
│   ├── __init__.py
│   ├── buyer_panel.py       # Buyer verification panel
│   └── admin_commands.py    # Admin management commands
├── utils/
│   ├── __init__.py
│   ├── database.py          # JSON database management
│   └── sellhub_api.py       # SellHub API integration
├── data/
│   ├── .gitkeep
│   ├── database.json        # Main database (auto-created)
│   └── database_backup.json # Automatic backup
├── requirements.txt         # Python dependencies
├── .env.example            # Environment variables template
└── README.md               # This file
```

## Database Structure

The bot uses a JSON database with the following structure:

```json
{
  "users": {
    "user_id": {
      "invoice_id": "inv_123",
      "email": "<EMAIL>",
      "verified_at": "2023-12-01T12:00:00",
      "discord_tag": "username#1234"
    }
  },
  "invoices": {
    "inv_123": {
      "user_id": *********,
      "email": "<EMAIL>",
      "verified_at": "2023-12-01T12:00:00",
      "status": "verified"
    }
  },
  "emails": {
    "<EMAIL>": {
      "user_id": *********,
      "invoice_id": "inv_123",
      "verified_at": "2023-12-01T12:00:00"
    }
  },
  "settings": {
    "created_at": "2023-12-01T12:00:00",
    "last_backup": "2023-12-01T12:00:00",
    "total_verifications": 1
  }
}
```

## Security Features

- **One invoice per user**: Each invoice can only be used once
- **One email per user**: Each email can only be used once
- **One verification per Discord account**: Each Discord user can only verify once
- **Admin-only commands**: Sensitive commands restricted to authorized users
- **Automatic backups**: Database is automatically backed up before changes
- **Input validation**: All inputs are validated and sanitized

## Troubleshooting

### Common Issues

1. **Bot not responding to slash commands**
   - Ensure the bot has the "Use Slash Commands" permission
   - Check if commands are synced (restart the bot)

2. **Cannot assign roles**
   - Ensure the bot's role is higher than the "Buyer" role
   - Check if the bot has "Manage Roles" permission

3. **SellHub API errors**
   - Verify your API token is correct
   - Check if the invoice ID format is valid
   - Ensure the invoice is paid

4. **Database errors**
   - Check file permissions in the `data/` directory
   - Ensure the bot has write access to the directory

### Support

If you encounter issues:
1. Check the console output for error messages
2. Verify your configuration in `config/settings.py`
3. Ensure all dependencies are installed correctly
4. Check Discord bot permissions

## License

This project is provided as-is for educational and commercial use.
