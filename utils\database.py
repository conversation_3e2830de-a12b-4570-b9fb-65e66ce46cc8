import json
import os
import shutil
from datetime import datetime
from typing import Dict, Optional, Any
from config.settings import DATABASE_FILE, BACKUP_DATABASE_FILE

class Database:
    def __init__(self):
        self.db_file = DATABASE_FILE
        self.backup_file = BACKUP_DATABASE_FILE
        self.ensure_data_directory()
        self.load_database()

    def ensure_data_directory(self):
        """Ensure the data directory exists"""
        os.makedirs(os.path.dirname(self.db_file), exist_ok=True)

    def load_database(self):
        """Load the database from file or create a new one"""
        if os.path.exists(self.db_file):
            try:
                with open(self.db_file, 'r', encoding='utf-8') as f:
                    self.data = json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                print("Database file corrupted or not found. Creating new database.")
                self.create_new_database()
        else:
            self.create_new_database()

    def create_new_database(self):
        """Create a new database structure"""
        self.data = {
            "users": {},  # user_id: {invoice_id, email, verified_at, discord_tag}
            "invoices": {},  # invoice_id: {user_id, email, verified_at, status}
            "emails": {},  # email: {user_id, invoice_id, verified_at}
            "settings": {
                "created_at": datetime.now().isoformat(),
                "last_backup": None,
                "total_verifications": 0
            }
        }
        self.save_database()

    def save_database(self):
        """Save the database to file"""
        try:
            # Create backup before saving
            if os.path.exists(self.db_file):
                shutil.copy2(self.db_file, self.backup_file)
                self.data["settings"]["last_backup"] = datetime.now().isoformat()

            with open(self.db_file, 'w', encoding='utf-8') as f:
                json.dump(self.data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Error saving database: {e}")

    def is_user_verified(self, user_id: int) -> bool:
        """Check if a user is already verified"""
        return str(user_id) in self.data["users"]

    def is_invoice_used(self, invoice_id: str) -> bool:
        """Check if an invoice is already used"""
        return invoice_id in self.data["invoices"]

    def is_email_used(self, email: str) -> bool:
        """Check if an email is already used"""
        return email in self.data["emails"]

    def add_verification(self, user_id: int, invoice_id: str, email: str, discord_tag: str) -> bool:
        """Add a new verification record"""
        try:
            user_id_str = str(user_id)
            timestamp = datetime.now().isoformat()

            # Add user record
            self.data["users"][user_id_str] = {
                "invoice_id": invoice_id,
                "email": email,
                "verified_at": timestamp,
                "discord_tag": discord_tag
            }

            # Add invoice record
            self.data["invoices"][invoice_id] = {
                "user_id": user_id,
                "email": email,
                "verified_at": timestamp,
                "status": "verified"
            }

            # Add email record
            self.data["emails"][email] = {
                "user_id": user_id,
                "invoice_id": invoice_id,
                "verified_at": timestamp
            }

            # Update statistics
            self.data["settings"]["total_verifications"] += 1

            self.save_database()
            return True
        except Exception as e:
            print(f"Error adding verification: {e}")
            return False

    def remove_user_verification(self, user_id: int) -> bool:
        """Remove a user's verification"""
        try:
            user_id_str = str(user_id)
            if user_id_str not in self.data["users"]:
                return False

            user_data = self.data["users"][user_id_str]
            invoice_id = user_data["invoice_id"]
            email = user_data["email"]

            # Remove from all records
            del self.data["users"][user_id_str]
            if invoice_id in self.data["invoices"]:
                del self.data["invoices"][invoice_id]
            if email in self.data["emails"]:
                del self.data["emails"][email]

            self.save_database()
            return True
        except Exception as e:
            print(f"Error removing verification: {e}")
            return False

    def get_user_data(self, user_id: int) -> Optional[Dict[str, Any]]:
        """Get user verification data"""
        return self.data["users"].get(str(user_id))

    def get_all_verified_users(self) -> Dict[str, Any]:
        """Get all verified users"""
        return self.data["users"]

    def clear_database(self) -> bool:
        """Clear all verification data"""
        try:
            self.data["users"] = {}
            self.data["invoices"] = {}
            self.data["emails"] = {}
            self.data["settings"]["total_verifications"] = 0
            self.save_database()
            return True
        except Exception as e:
            print(f"Error clearing database: {e}")
            return False

    def get_stats(self) -> Dict[str, Any]:
        """Get database statistics"""
        return {
            "total_users": len(self.data["users"]),
            "total_invoices": len(self.data["invoices"]),
            "total_emails": len(self.data["emails"]),
            "total_verifications": self.data["settings"]["total_verifications"],
            "created_at": self.data["settings"]["created_at"],
            "last_backup": self.data["settings"]["last_backup"]
        }
