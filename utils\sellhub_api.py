import aiohttp
import os
from typing import Optional, Dict, Any
from config.settings import SELLHUB_API_BASE_URL

class SellHubAPI:
    def __init__(self):
        self.api_key = os.getenv('SELLHUB_API_TOKEN')
        self.base_url = SELLHUB_API_BASE_URL

        if not self.api_key:
            raise ValueError("SELLHUB_API_TOKEN not found in environment variables")

    async def get_invoice(self, invoice_id: str) -> Optional[Dict[str, Any]]:
        """
        Get invoice details from SellHub API
        Returns invoice data if successful, None if failed
        """
        url = f"{self.base_url}/invoices/{invoice_id}"
        headers = {
            "Authorization": self.api_key,
            "Content-Type": "application/json"
        }

        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data.get("data", {}).get("invoice")
                    elif response.status == 401:
                        print("SellHub API: Unauthorized - Check API key")
                        return None
                    elif response.status == 404:
                        print(f"SellHub API: Invoice {invoice_id} not found")
                        return None
                    else:
                        print(f"SellHub API: Error {response.status}")
                        return None
        except aiohttp.ClientError as e:
            print(f"SellHub API: Connection error - {e}")
            return None
        except Exception as e:
            print(f"SellHub API: Unexpected error - {e}")
            return None

    async def validate_invoice(self, invoice_id: str) -> Dict[str, Any]:
        """
        Validate an invoice and return validation result
        Returns dict with validation status and details
        """
        result = {
            "valid": False,
            "invoice_data": None,
            "error": None,
            "email": None,
            "status": None,
            "total": None,
            "discord_account": None
        }

        invoice_data = await self.get_invoice(invoice_id)

        if not invoice_data:
            result["error"] = "Invoice not found or API error"
            return result

        # Check if invoice is paid or completed
        status = invoice_data.get("status")
        if status not in ["paid", "completed"]:
            result["error"] = f"Invoice status is '{status}', must be 'paid' or 'completed'"
            return result

        # Extract important information
        result["valid"] = True
        result["invoice_data"] = invoice_data
        result["status"] = invoice_data.get("status")
        result["total"] = invoice_data.get("totalInUsd")

        # Get email from customer or discord account
        discord_account = invoice_data.get("discordAccount")
        if discord_account:
            result["email"] = discord_account.get("email")
            result["discord_account"] = discord_account

        # If no discord account, try to get customer email
        if not result["email"]:
            customer_id = invoice_data.get("customerId")
            if customer_id:
                # Note: You might need to implement get_customer method if needed
                result["email"] = "customer_email_not_available"

        return result

    async def test_connection(self) -> bool:
        """Test if the API connection is working"""
        try:
            # Try to get a non-existent invoice to test auth
            url = f"{self.base_url}/invoices/test_connection_123"
            headers = {
                "Authorization": self.api_key,
                "Content-Type": "application/json"
            }

            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers) as response:
                    # If we get 404, it means auth is working
                    # If we get 401, it means auth is not working
                    return response.status != 401
        except Exception:
            return False
