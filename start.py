#!/usr/bin/env python3
"""
Startup script for the Discord SellHub Bot
Includes pre-flight checks and graceful error handling
"""

import os
import sys
import asyncio
from dotenv import load_dotenv
from utils.logger import print_banner, print_shutdown_banner, log_info, log_success, log_error, log_warning

def check_requirements():
    """Check if all requirements are met"""
    log_info("🔍 Checking requirements...")

    # Check Python version
    if sys.version_info < (3, 8):
        log_error("❌ Python 3.8 or higher is required")
        return False

    # Check if .env file exists
    if not os.path.exists('.env'):
        log_error("❌ .env file not found. Please create one based on .env.example")
        return False

    # Load environment variables
    load_dotenv()

    # Check required environment variables
    required_vars = ['BOT_TOKEN', 'SELLHUB_API_TOKEN']
    for var in required_vars:
        if not os.getenv(var):
            log_error(f"❌ {var} not found in .env file")
            return False

    # Check if data directory exists
    if not os.path.exists('data'):
        log_info("📁 Creating data directory...")
        os.makedirs('data', exist_ok=True)

    log_success("✅ All requirements met!")
    return True

def check_imports():
    """Check if all required packages are installed"""
    log_info("🔍 Checking imports...")

    required_packages = [
        ('discord', 'discord.py'),
        ('aiohttp', 'aiohttp'),
        ('dotenv', 'python-dotenv')
    ]

    for package, pip_name in required_packages:
        try:
            __import__(package)
            log_success(f"✅ {pip_name} is installed")
        except ImportError:
            log_error(f"❌ {pip_name} is not installed. Run: pip install {pip_name}")
            return False

    return True

async def start_bot():
    """Start the bot with error handling"""
    try:
        print("🚀 Starting Discord SellHub Bot...")

        # Import and run the main bot
        from index import main
        await main()

    except KeyboardInterrupt:
        print("\n🛑 Bot stopped by user")
    except Exception as e:
        print(f"\n❌ Bot crashed with error: {e}")
        print("Please check your configuration and try again.")
        sys.exit(1)

def main():
    """Main startup function"""
    print_banner()

    # Run pre-flight checks
    if not check_requirements():
        log_error("\n❌ Pre-flight checks failed. Please fix the issues above.")
        sys.exit(1)

    if not check_imports():
        log_error("\n❌ Import checks failed. Please install missing packages.")
        log_info("Run: pip install -r requirements.txt")
        sys.exit(1)

    log_success("\n✅ All checks passed! Starting bot...")

    # Start the bot
    try:
        asyncio.run(start_bot())
    except KeyboardInterrupt:
        print_shutdown_banner()
    except RuntimeError as e:
        if "Event loop stopped" in str(e):
            # This is expected during shutdown, ignore it
            print_shutdown_banner()
        else:
            print(f"\n❌ Runtime error: {e}")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        print_shutdown_banner()

if __name__ == "__main__":
    main()
