{"bots": [{"name": "MAIN", "script": "start.py", "env_file": ".env", "color": "bright_green", "description": "Main Discord bot instance"}, {"name": "TEST", "script": "start.py", "env_file": ".env.test", "color": "bright_blue", "description": "Test Discord bot instance", "enabled": false}, {"name": "DEV", "script": "start.py", "env_file": ".env.dev", "color": "bright_yellow", "description": "Development Discord bot instance", "enabled": false}], "settings": {"start_delay": 1, "restart_delay": 2, "log_timestamps": true, "auto_restart_on_crash": false}}