#!/usr/bin/env python3
"""
Multi-Bot Launcher
Launches multiple Discord bot instances and displays their console logs in real-time.
"""

import asyncio
import subprocess
import sys
import os
import threading
import time
from datetime import datetime
from typing import Dict, List, Optional
import signal
import json

class Colors:
    """ANSI color codes for terminal output"""
    RESET = '\033[0m'
    BOLD = '\033[1m'

    # Regular colors
    BLACK = '\033[30m'
    RED = '\033[31m'
    GREEN = '\033[32m'
    YELLOW = '\033[33m'
    BLUE = '\033[34m'
    MAGENTA = '\033[35m'
    CYAN = '\033[36m'
    WHITE = '\033[37m'

    # Bright colors
    BRIGHT_BLACK = '\033[90m'
    BRIGHT_RED = '\033[91m'
    BRIGHT_GREEN = '\033[92m'
    BRIGHT_YELLOW = '\033[93m'
    BRIGHT_BLUE = '\033[94m'
    BRIGHT_MAGENTA = '\033[95m'
    BRIGHT_CYAN = '\033[96m'
    BRIGHT_WHITE = '\033[97m'

class BotInstance:
    """Represents a single bot instance"""

    def __init__(self, name: str, script_path: str, env_file: str = None, color: str = Colors.WHITE):
        self.name = name
        self.script_path = script_path
        self.env_file = env_file
        self.color = color
        self.process: Optional[subprocess.Popen] = None
        self.is_running = False
        self.start_time: Optional[datetime] = None

    def start(self):
        """Start the bot instance"""
        try:
            # Set up environment
            env = os.environ.copy()
            if self.env_file and os.path.exists(self.env_file):
                # Load environment variables from file
                with open(self.env_file, 'r') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#') and '=' in line:
                            key, value = line.split('=', 1)
                            env[key.strip()] = value.strip().strip('"\'')

            # Start the process
            self.process = subprocess.Popen(
                [sys.executable, self.script_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1,
                env=env
            )

            self.is_running = True
            self.start_time = datetime.now()

            # Start log reader thread
            log_thread = threading.Thread(target=self._read_logs, daemon=True)
            log_thread.start()

            return True

        except Exception as e:
            self._log(f"❌ Failed to start: {e}", Colors.BRIGHT_RED)
            return False

    def stop(self):
        """Stop the bot instance"""
        if self.process and self.is_running:
            try:
                self.process.terminate()
                self.process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.process.kill()
                self.process.wait()
            except Exception as e:
                self._log(f"❌ Error stopping: {e}", Colors.BRIGHT_RED)

            self.is_running = False
            self._log("🛑 Bot stopped", Colors.BRIGHT_YELLOW)

    def restart(self):
        """Restart the bot instance"""
        self._log("🔄 Restarting bot...", Colors.BRIGHT_YELLOW)
        self.stop()
        time.sleep(2)
        return self.start()

    def _read_logs(self):
        """Read and display logs from the bot process"""
        if not self.process:
            return

        try:
            for line in iter(self.process.stdout.readline, ''):
                if line:
                    # Remove trailing newline and display with bot prefix
                    clean_line = line.rstrip('\n\r')
                    if clean_line:
                        self._log(clean_line)

                # Check if process is still running
                if self.process.poll() is not None:
                    break

        except Exception as e:
            self._log(f"❌ Log reader error: {e}", Colors.BRIGHT_RED)
        finally:
            self.is_running = False
            if self.process and self.process.poll() is not None:
                exit_code = self.process.poll()
                if exit_code != 0:
                    self._log(f"💥 Bot crashed with exit code {exit_code}", Colors.BRIGHT_RED)
                else:
                    self._log("✅ Bot exited normally", Colors.BRIGHT_GREEN)

    def _log(self, message: str, color: str = None):
        """Log a message with bot prefix"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        color = color or self.color

        # Format: [HH:MM:SS] [BOT_NAME] message
        formatted_message = f"{Colors.BRIGHT_BLACK}[{timestamp}]{Colors.RESET} {color}[{self.name}]{Colors.RESET} {message}"
        print(formatted_message)

    def get_status(self) -> Dict:
        """Get bot status information"""
        uptime = None
        if self.start_time and self.is_running:
            uptime = datetime.now() - self.start_time

        return {
            'name': self.name,
            'running': self.is_running,
            'uptime': str(uptime) if uptime else None,
            'pid': self.process.pid if self.process else None
        }

class MultiBotLauncher:
    """Main launcher class"""

    def __init__(self):
        self.bots: List[BotInstance] = []
        self.running = False

        # Set up signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def add_bot(self, name: str, script_path: str, env_file: str = None, color: str = Colors.WHITE):
        """Add a bot instance to the launcher"""
        if not os.path.exists(script_path):
            print(f"{Colors.BRIGHT_RED}❌ Script not found: {script_path}{Colors.RESET}")
            return False

        bot = BotInstance(name, script_path, env_file, color)
        self.bots.append(bot)
        print(f"{Colors.BRIGHT_GREEN}✅ Added bot: {name}{Colors.RESET}")
        return True

    def start_all(self):
        """Start all bot instances"""
        if not self.bots:
            print(f"{Colors.BRIGHT_RED}❌ No bots configured{Colors.RESET}")
            return

        print(f"\n{Colors.BRIGHT_CYAN}🚀 Starting {len(self.bots)} bot instance(s)...{Colors.RESET}\n")

        self.running = True

        for bot in self.bots:
            if bot.start():
                print(f"{Colors.BRIGHT_GREEN}✅ Started: {bot.name}{Colors.RESET}")
            else:
                print(f"{Colors.BRIGHT_RED}❌ Failed to start: {bot.name}{Colors.RESET}")
            time.sleep(1)  # Small delay between starts

        print(f"\n{Colors.BRIGHT_CYAN}📊 All bots started. Press Ctrl+C to stop all bots.{Colors.RESET}\n")

        # Keep the main thread alive
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            pass

    def stop_all(self):
        """Stop all bot instances"""
        print(f"\n{Colors.BRIGHT_YELLOW}🛑 Stopping all bots...{Colors.RESET}")

        self.running = False

        for bot in self.bots:
            if bot.is_running:
                bot.stop()

        print(f"{Colors.BRIGHT_GREEN}✅ All bots stopped{Colors.RESET}")

    def restart_all(self):
        """Restart all bot instances"""
        print(f"\n{Colors.BRIGHT_YELLOW}🔄 Restarting all bots...{Colors.RESET}")

        for bot in self.bots:
            if bot.is_running:
                bot.restart()

    def show_status(self):
        """Show status of all bots"""
        print(f"\n{Colors.BRIGHT_CYAN}📊 Bot Status:{Colors.RESET}")
        print("=" * 60)

        for bot in self.bots:
            status = bot.get_status()
            status_color = Colors.BRIGHT_GREEN if status['running'] else Colors.BRIGHT_RED
            status_text = "RUNNING" if status['running'] else "STOPPED"

            print(f"{bot.color}{status['name']:<15}{Colors.RESET} {status_color}{status_text:<8}{Colors.RESET}", end="")

            if status['uptime']:
                print(f" Uptime: {status['uptime']}", end="")
            if status['pid']:
                print(f" PID: {status['pid']}", end="")
            print()

        print("=" * 60)

    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        print(f"\n{Colors.BRIGHT_YELLOW}🛑 Received shutdown signal{Colors.RESET}")
        self.stop_all()
        sys.exit(0)

def print_banner():
    """Print the launcher banner"""
    banner = f"""
{Colors.BRIGHT_CYAN}╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║  🚀 Multi-Bot Launcher                                      ║
║                                                              ║
║  Launch and manage multiple Discord bot instances           ║
║  with real-time console log monitoring                      ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝{Colors.RESET}
"""
    print(banner)

def load_config(config_file: str = 'bot_configs.json') -> Dict:
    """Load bot configurations from JSON file"""
    try:
        if os.path.exists(config_file):
            with open(config_file, 'r') as f:
                return json.load(f)
        else:
            print(f"{Colors.BRIGHT_YELLOW}⚠️ Config file not found: {config_file}{Colors.RESET}")
            return {"bots": [], "settings": {}}
    except Exception as e:
        print(f"{Colors.BRIGHT_RED}❌ Error loading config: {e}{Colors.RESET}")
        return {"bots": [], "settings": {}}

def get_color_from_name(color_name: str) -> str:
    """Convert color name to ANSI color code"""
    color_map = {
        'black': Colors.BLACK,
        'red': Colors.RED,
        'green': Colors.GREEN,
        'yellow': Colors.YELLOW,
        'blue': Colors.BLUE,
        'magenta': Colors.MAGENTA,
        'cyan': Colors.CYAN,
        'white': Colors.WHITE,
        'bright_black': Colors.BRIGHT_BLACK,
        'bright_red': Colors.BRIGHT_RED,
        'bright_green': Colors.BRIGHT_GREEN,
        'bright_yellow': Colors.BRIGHT_YELLOW,
        'bright_blue': Colors.BRIGHT_BLUE,
        'bright_magenta': Colors.BRIGHT_MAGENTA,
        'bright_cyan': Colors.BRIGHT_CYAN,
        'bright_white': Colors.BRIGHT_WHITE,
    }
    return color_map.get(color_name.lower(), Colors.WHITE)

def main():
    """Main function"""
    print_banner()

    # Load configuration
    config = load_config()

    # Create launcher instance
    launcher = MultiBotLauncher()

    # Add bots from configuration
    for bot_config in config.get('bots', []):
        # Skip disabled bots
        if not bot_config.get('enabled', True):
            print(f"{Colors.BRIGHT_BLACK}⏭️ Skipping disabled bot: {bot_config['name']}{Colors.RESET}")
            continue

        color = get_color_from_name(bot_config.get('color', 'white'))

        launcher.add_bot(
            name=bot_config['name'],
            script_path=bot_config['script'],
            env_file=bot_config.get('env_file'),
            color=color
        )

    # If no bots in config, add default
    if not launcher.bots:
        print(f"{Colors.BRIGHT_YELLOW}⚠️ No bots configured, adding default bot{Colors.RESET}")
        launcher.add_bot(
            name='MAIN',
            script_path='start.py',
            env_file='.env',
            color=Colors.BRIGHT_GREEN
        )

    # Start all bots
    try:
        launcher.start_all()
    except KeyboardInterrupt:
        pass
    finally:
        launcher.stop_all()

if __name__ == "__main__":
    main()
