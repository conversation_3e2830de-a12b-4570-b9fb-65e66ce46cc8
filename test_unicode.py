#!/usr/bin/env python3
"""
Test Unicode support in console
"""

import sys
import os

# Fix Windows console encoding for Unicode support
if sys.platform == "win32":
    try:
        import codecs
        sys.stdout = codecs.getwriter("utf-8")(sys.stdout.detach())
        sys.stderr = codecs.getwriter("utf-8")(sys.stderr.detach())
        # Also try to set console code page to UTF-8
        os.system("chcp 65001 >nul 2>&1")
    except Exception as e:
        print(f"Failed to set UTF-8: {e}")

print(f"Python version: {sys.version}")
print(f"Console encoding: {sys.stdout.encoding}")
print("Testing Unicode emojis:")
print("🚀 Rocket")
print("🤖 Robot") 
print("✅ Check mark")
print("📊 Chart")
print("🔍 Magnifying glass")
print("⚡ Lightning")
print("🛑 Stop sign")
print("Colors test:")
print("\033[92m✅ Green text\033[0m")
print("\033[91m❌ Red text\033[0m")
print("\033[94m🔵 Blue text\033[0m")
print("Test completed!")
