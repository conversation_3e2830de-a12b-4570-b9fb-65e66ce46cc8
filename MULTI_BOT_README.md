# 🚀 Multi-Bot Launcher

Launch and manage multiple Discord bot instances with real-time console log monitoring.

## ✨ Features

- **Multiple Bot Instances**: Run several bots simultaneously
- **Real-time Logs**: See console output from all bots with color-coded prefixes
- **Individual Management**: Start, stop, restart individual bots
- **Configuration-based**: JSON configuration for easy bot management
- **Interactive Interface**: Menu-driven interface for easy control
- **Environment Isolation**: Each bot can use different environment files
- **Graceful Shutdown**: Proper cleanup when stopping bots
- **Status Monitoring**: View uptime, PID, and status of each bot

## 📁 Files

- `multi_bot_launcher.py` - Main launcher script (automatic mode)
- `interactive_launcher.py` - Interactive menu-driven launcher
- `bot_configs.json` - Configuration file for bot instances
- `.env.example.test` - Example environment file for additional bots

## 🚀 Quick Start

### 1. Basic Usage (Single Bot)
```bash
python multi_bot_launcher.py
```

### 2. Interactive Mode
```bash
python interactive_launcher.py
```

### 3. Configure Multiple Bots

Edit `bot_configs.json`:
```json
{
  "bots": [
    {
      "name": "MAIN",
      "script": "start.py",
      "env_file": ".env",
      "color": "bright_green",
      "description": "Main Discord bot instance"
    },
    {
      "name": "TEST",
      "script": "start.py",
      "env_file": ".env.test",
      "color": "bright_blue",
      "description": "Test Discord bot instance",
      "enabled": true
    }
  ]
}
```

### 4. Create Environment Files

For each bot instance, create a separate `.env` file:

**Main bot**: `.env` (your existing file)
**Test bot**: `.env.test`
```env
BOT_TOKEN=your_test_bot_token_here
SELLHUB_API_TOKEN=your_sellhub_api_token_here
```

## 🎨 Console Output

Each bot's logs are color-coded and prefixed:

```
[17:30:15] [MAIN] 🚀 Starting Discord SellHub Bot...
[17:30:16] [TEST] 🚀 Starting Discord SellHub Bot...
[17:30:18] [MAIN] 🤖 Divine Roles#2849 has connected to Discord!
[17:30:19] [TEST] 🤖 Test Bot#1234 has connected to Discord!
```

## ⚙️ Configuration Options

### Bot Configuration
- `name`: Display name for the bot instance
- `script`: Python script to run (usually `start.py`)
- `env_file`: Environment file path (e.g., `.env`, `.env.test`)
- `color`: Console color (`bright_green`, `bright_blue`, etc.)
- `description`: Human-readable description
- `enabled`: Whether to start this bot (true/false)

### Available Colors
- `bright_green`, `bright_blue`, `bright_yellow`, `bright_red`
- `bright_cyan`, `bright_magenta`, `bright_white`, `bright_black`
- `green`, `blue`, `yellow`, `red`, `cyan`, `magenta`, `white`, `black`

## 🎮 Interactive Commands

### Main Menu
1. **Start All Bots** - Launch all enabled bot instances
2. **Stop All Bots** - Stop all running bots
3. **Restart All Bots** - Restart all running bots
4. **Show Bot Status** - Display status, uptime, and PID
5. **Manage Individual Bots** - Control specific bots
6. **View Configuration** - Show current bot configuration
0. **Exit** - Quit the launcher

### Individual Bot Management
- Start/Stop specific bots
- Restart individual bots
- View detailed bot information

## 🛠️ Use Cases

### Development & Testing
```json
{
  "bots": [
    {
      "name": "PROD",
      "script": "start.py",
      "env_file": ".env",
      "color": "bright_green"
    },
    {
      "name": "DEV",
      "script": "start.py",
      "env_file": ".env.dev",
      "color": "bright_yellow"
    }
  ]
}
```

### Multiple Servers
```json
{
  "bots": [
    {
      "name": "SERVER1",
      "script": "start.py",
      "env_file": ".env.server1",
      "color": "bright_blue"
    },
    {
      "name": "SERVER2",
      "script": "start.py",
      "env_file": ".env.server2",
      "color": "bright_cyan"
    }
  ]
}
```

## 🔧 Advanced Usage

### Custom Scripts
You can run different scripts for different bots:
```json
{
  "name": "SPECIAL",
  "script": "custom_bot.py",
  "env_file": ".env.special",
  "color": "bright_magenta"
}
```

### Environment Variables
Each bot can have completely different environment variables by using separate `.env` files.

## 🚨 Important Notes

1. **Bot Tokens**: Each bot instance needs its own Discord bot token
2. **Database Files**: Consider using different database files for each instance
3. **Log Channels**: You may want different log channels for each bot
4. **Rate Limits**: Be aware of Discord API rate limits when running multiple bots

## 🛑 Stopping Bots

- **Ctrl+C**: Gracefully stops all bots
- **Interactive Mode**: Use menu options to stop individual or all bots
- **Automatic Cleanup**: All bots are properly terminated on exit

## 📊 Monitoring

The launcher provides real-time monitoring:
- Bot status (RUNNING/STOPPED)
- Uptime tracking
- Process ID (PID)
- Console log output
- Crash detection and reporting

## 🔍 Troubleshooting

### Bot Won't Start
- Check if the script path exists
- Verify environment file exists and has correct tokens
- Ensure no port conflicts

### Logs Not Showing
- Check if the bot script produces console output
- Verify the script runs correctly when started manually

### Permission Errors
- Ensure the launcher has permission to start processes
- Check file permissions on scripts and environment files
