import discord
from discord.ext import commands, tasks
import os
import asyncio
import signal
import sys
from dotenv import load_dotenv
from config.settings import *
from commands.buyer_panel import BuyerPanel
from commands.admin_commands import AdminCommands
from utils.database import Database
from utils.logger import setup_logger, log_info, log_success, log_error, log_warning, print_banner, print_shutdown_banner

# Load environment variables
load_dotenv()

# Setup colorful logging
logger = setup_logger()

# Bot setup
intents = discord.Intents.default()
intents.message_content = True
intents.guilds = True
intents.members = True

bot = commands.Bot(command_prefix='!', intents=intents)

# Initialize database
db = Database()

# Global variable to track if we're shutting down
shutting_down = False

# Status messages to rotate
STATUS_MESSAGES = [
    "Buy at: Divine.land",
    "Always provide invoice-ids",
    "Fastest support!",
    "E is the best management",
    "Inquires? Open a ticket"
]
current_status_index = 0

def signal_handler(signum, frame):
    """Handle Ctrl+C gracefully"""
    global shutting_down
    if not shutting_down:
        shutting_down = True
        log_warning("🛑 Received interrupt signal (Ctrl+C)")
        log_info("🔄 Gracefully shutting down bot...")
        asyncio.create_task(shutdown_bot())

def setup_signal_handlers():
    """Setup signal handlers for graceful shutdown"""
    signal.signal(signal.SIGINT, signal_handler)
    if hasattr(signal, 'SIGTERM'):
        signal.signal(signal.SIGTERM, signal_handler)

@tasks.loop(seconds=8)
async def rotate_status():
    """Rotate bot status messages every 8 seconds"""
    global current_status_index
    if not shutting_down and bot.is_ready():
        try:
            status_message = STATUS_MESSAGES[current_status_index]
            activity = discord.Activity(type=discord.ActivityType.watching, name=status_message)
            await bot.change_presence(activity=activity)
            log_info(f"🔄 Status updated: {status_message}")

            # Move to next status
            current_status_index = (current_status_index + 1) % len(STATUS_MESSAGES)
        except Exception as e:
            log_error(f"❌ Error updating status: {e}")

async def shutdown_bot():
    """Gracefully shutdown the bot"""
    global shutting_down
    if shutting_down:
        try:
            log_info("🛑 Stopping status rotation...")
            rotate_status.stop()

            log_info("💾 Saving database...")
            db.save_database()
            log_success("✅ Database saved successfully")

            log_info("🔌 Closing bot connection...")
            await bot.close()
            log_success("✅ Bot connection closed")

            log_success("👋 Bot shutdown complete. Goodbye!")
            print_shutdown_banner()
            sys.exit(0)
        except Exception as e:
            log_error(f"❌ Error during shutdown: {e}")
            sys.exit(1)

@bot.event
async def on_ready():
    log_success(f'🤖 {bot.user} has connected to Discord!')
    log_info(f'🏠 Bot is in {len(bot.guilds)} guilds')

    # Sync slash commands
    try:
        synced = await bot.tree.sync()
        log_success(f'⚡ Synced {len(synced)} slash command(s)')

        # Log available commands
        for command in bot.tree.get_commands():
            log_info(f"  📋 /{command.name} - {command.description}")

        # Start status rotation
        if not rotate_status.is_running():
            rotate_status.start()
            log_success("🔄 Status rotation started")

    except Exception as e:
        log_error(f'❌ Failed to sync commands: {e}')

@bot.event
async def on_guild_join(guild):
    log_success(f'🎉 Joined new guild: {guild.name} (ID: {guild.id})')
    log_info(f'👥 Guild has {guild.member_count} members')

@bot.event
async def on_guild_remove(guild):
    log_warning(f'👋 Left guild: {guild.name} (ID: {guild.id})')

@bot.event
async def on_application_command_error(interaction, error):
    """Handle slash command errors"""
    log_error(f"❌ Command error in /{interaction.command.name}: {error}")

    if not interaction.response.is_done():
        embed = discord.Embed(
            title="❌ Command Error",
            description="An unexpected error occurred while processing your command.",
            color=EMBED_COLOR_ERROR
        )
        embed.add_field(
            name="Error Details",
            value=f"```{str(error)[:1000]}```",
            inline=False
        )
        embed.set_footer(text="Please contact an administrator if this persists.")

        try:
            await interaction.response.send_message(embed=embed, ephemeral=True)
        except:
            await interaction.followup.send(embed=embed, ephemeral=True)

# Add cogs
async def setup_cogs():
    log_info("🔧 Loading bot modules...")
    await bot.add_cog(BuyerPanel(bot, db))
    log_success("✅ Buyer Panel module loaded")
    await bot.add_cog(AdminCommands(bot, db))
    log_success("✅ Admin Commands module loaded")

async def main():
    """Main bot function with enhanced error handling"""
    setup_signal_handlers()

    try:
        log_info("🚀 Starting Discord SellHub Bot...")
        log_info("🔑 Loading environment variables...")

        # Check if required tokens exist
        bot_token = os.getenv('BOT_TOKEN')
        sellhub_token = os.getenv('SELLHUB_API_TOKEN')

        if not bot_token:
            log_error("❌ BOT_TOKEN not found in environment variables")
            sys.exit(1)
        if not sellhub_token:
            log_error("❌ SELLHUB_API_TOKEN not found in environment variables")
            sys.exit(1)

        log_success("✅ Environment variables loaded")

        async with bot:
            await setup_cogs()
            log_info("🌐 Connecting to Discord...")
            await bot.start(bot_token)

    except KeyboardInterrupt:
        log_warning("🛑 Received keyboard interrupt")
        await shutdown_bot()
    except Exception as e:
        log_error(f"❌ Fatal error: {e}")
        sys.exit(1)

if __name__ == '__main__':
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Exiting...")
        sys.exit(0)