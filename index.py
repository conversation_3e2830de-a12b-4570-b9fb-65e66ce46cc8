import discord
from discord.ext import commands
import os
import asyncio
from dotenv import load_dotenv
from config.settings import *
from commands.buyer_panel import BuyerPanel
from commands.admin_commands import AdminCommands
from utils.database import Database

# Load environment variables
load_dotenv()

# Bot setup
intents = discord.Intents.default()
intents.message_content = True
intents.guilds = True
intents.members = True

bot = commands.Bot(command_prefix='!', intents=intents)

# Initialize database
db = Database()

@bot.event
async def on_ready():
    print(f'{bot.user} has connected to Discord!')
    print(f'Bot is in {len(bot.guilds)} guilds')

    # Sync slash commands
    try:
        synced = await bot.tree.sync()
        print(f'Synced {len(synced)} command(s)')
    except Exception as e:
        print(f'Failed to sync commands: {e}')

@bot.event
async def on_guild_join(guild):
    print(f'Joined guild: {guild.name} (ID: {guild.id})')

# Add cogs
async def setup_cogs():
    await bot.add_cog(BuyerPanel(bot, db))
    await bot.add_cog(AdminCommands(bot, db))

async def main():
    async with bot:
        await setup_cogs()
        await bot.start(os.getenv('BOT_TOKEN'))

if __name__ == '__main__':
    asyncio.run(main())