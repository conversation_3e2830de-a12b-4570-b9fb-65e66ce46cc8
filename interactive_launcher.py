#!/usr/bin/env python3
"""
Interactive Multi-Bot Launcher
File-based interface for selecting and managing multiple Discord bots.
"""

import os
import sys
import time
import glob
from multi_bot_launcher import MultiBotLauncher, Colors, print_banner, load_config, get_color_from_name

class InteractiveLauncher:
    """Interactive launcher with file selection interface"""

    def __init__(self):
        self.launcher = MultiBotLauncher()
        self.available_scripts = self.find_bot_scripts()
        self.available_env_files = self.find_env_files()
        self.selected_bots = []

    def find_bot_scripts(self):
        """Find all Python scripts that could be bot files"""
        scripts = []

        # Look for common bot script patterns
        patterns = ['*.py', 'bots/*.py', 'src/*.py']

        for pattern in patterns:
            for script in glob.glob(pattern):
                if os.path.isfile(script) and not script.startswith('__'):
                    scripts.append(script)

        # Remove duplicates and sort
        scripts = sorted(list(set(scripts)))

        # Filter out obvious non-bot files
        exclude_patterns = ['setup.py', 'test_', 'tests/', '__pycache__', 'multi_bot_launcher.py', 'interactive_launcher.py']
        filtered_scripts = []

        for script in scripts:
            if not any(exclude in script for exclude in exclude_patterns):
                filtered_scripts.append(script)

        return filtered_scripts

    def find_env_files(self):
        """Find all .env files"""
        env_files = glob.glob('.env*')
        return sorted([f for f in env_files if os.path.isfile(f)])

    def show_available_scripts(self):
        """Display available Python scripts"""
        print(f"\n{Colors.BRIGHT_CYAN}📁 Available Bot Scripts{Colors.RESET}")
        print("=" * 50)

        if not self.available_scripts:
            print(f"{Colors.BRIGHT_RED}❌ No Python scripts found{Colors.RESET}")
            return

        for i, script in enumerate(self.available_scripts, 1):
            # Check if script is already selected
            is_selected = any(bot.script_path == script for bot in self.selected_bots)
            status = f"{Colors.BRIGHT_GREEN}[SELECTED]{Colors.RESET}" if is_selected else ""

            print(f"{Colors.BRIGHT_WHITE}{i:2}.{Colors.RESET} {Colors.BRIGHT_YELLOW}{script:<30}{Colors.RESET} {status}")

        print("=" * 50)

    def show_env_files(self):
        """Display available environment files"""
        print(f"\n{Colors.BRIGHT_CYAN}� Available Environment Files{Colors.RESET}")
        print("=" * 40)

        if not self.available_env_files:
            print(f"{Colors.BRIGHT_RED}❌ No .env files found{Colors.RESET}")
            return

        for i, env_file in enumerate(self.available_env_files, 1):
            print(f"{Colors.BRIGHT_WHITE}{i}.{Colors.RESET} {Colors.BRIGHT_GREEN}{env_file}{Colors.RESET}")

        print("=" * 40)

    def show_selected_bots(self):
        """Display currently selected bots"""
        print(f"\n{Colors.BRIGHT_CYAN}🤖 Selected Bots{Colors.RESET}")
        print("=" * 60)

        if not self.selected_bots:
            print(f"{Colors.BRIGHT_YELLOW}⚠️ No bots selected{Colors.RESET}")
            return

        for i, bot in enumerate(self.selected_bots, 1):
            status_color = Colors.BRIGHT_GREEN if bot.is_running else Colors.BRIGHT_RED
            status_text = "RUNNING" if bot.is_running else "STOPPED"

            print(f"{Colors.BRIGHT_WHITE}{i}.{Colors.RESET} {bot.color}{bot.name:<15}{Colors.RESET} "
                  f"{Colors.BRIGHT_YELLOW}{bot.script_path:<20}{Colors.RESET} "
                  f"{Colors.BRIGHT_BLUE}{bot.env_file or 'No env':<10}{Colors.RESET} "
                  f"{status_color}[{status_text}]{Colors.RESET}")

        print("=" * 60)

    def show_main_menu(self):
        """Display the main action menu"""
        print(f"\n{Colors.BRIGHT_CYAN}⚡ Actions{Colors.RESET}")
        print("=" * 30)
        print(f"{Colors.BRIGHT_GREEN}1.{Colors.RESET} Add Bot")
        print(f"{Colors.BRIGHT_YELLOW}2.{Colors.RESET} Remove Bot")
        print(f"{Colors.BRIGHT_BLUE}3.{Colors.RESET} Start All Bots")
        print(f"{Colors.BRIGHT_MAGENTA}4.{Colors.RESET} Stop All Bots")
        print(f"{Colors.BRIGHT_CYAN}5.{Colors.RESET} Manage Individual Bots")
        print(f"{Colors.BRIGHT_WHITE}6.{Colors.RESET} Show Status")
        print(f"{Colors.BRIGHT_RED}0.{Colors.RESET} Exit")
        print("=" * 30)

    def show_individual_menu(self):
        """Display individual bot management menu"""
        print(f"\n{Colors.BRIGHT_CYAN}🤖 Individual Bot Management{Colors.RESET}")
        print("=" * 40)

        for i, bot in enumerate(self.launcher.bots, 1):
            status_color = Colors.BRIGHT_GREEN if bot.is_running else Colors.BRIGHT_RED
            status_text = "RUNNING" if bot.is_running else "STOPPED"
            print(f"{Colors.BRIGHT_WHITE}{i}.{Colors.RESET} {bot.color}{bot.name:<10}{Colors.RESET} [{status_color}{status_text}{Colors.RESET}]")

        print(f"{Colors.BRIGHT_RED}0.{Colors.RESET} Back to Main Menu")
        print("=" * 40)

    def show_bot_actions(self, bot_index: int):
        """Show actions for a specific bot"""
        bot = self.launcher.bots[bot_index]
        print(f"\n{Colors.BRIGHT_CYAN}🔧 Actions for {bot.color}{bot.name}{Colors.RESET}")
        print("=" * 30)

        if bot.is_running:
            print(f"{Colors.BRIGHT_RED}1.{Colors.RESET} Stop Bot")
            print(f"{Colors.BRIGHT_YELLOW}2.{Colors.RESET} Restart Bot")
        else:
            print(f"{Colors.BRIGHT_GREEN}1.{Colors.RESET} Start Bot")

        print(f"{Colors.BRIGHT_BLUE}3.{Colors.RESET} View Bot Info")
        print(f"{Colors.BRIGHT_RED}0.{Colors.RESET} Back")
        print("=" * 30)

    def show_config(self):
        """Display current configuration"""
        print(f"\n{Colors.BRIGHT_CYAN}⚙️ Current Configuration{Colors.RESET}")
        print("=" * 50)

        for bot_config in self.config.get('bots', []):
            enabled_text = "✅ ENABLED" if bot_config.get('enabled', True) else "❌ DISABLED"
            color = get_color_from_name(bot_config.get('color', 'white'))

            print(f"{color}{bot_config['name']:<10}{Colors.RESET} {enabled_text}")
            print(f"  Script: {bot_config['script']}")
            print(f"  Env: {bot_config.get('env_file', 'None')}")
            print(f"  Description: {bot_config.get('description', 'No description')}")
            print()

        print("=" * 50)

    def handle_individual_bot(self, bot_index: int):
        """Handle individual bot management"""
        bot = self.launcher.bots[bot_index]

        while True:
            self.show_bot_actions(bot_index)

            try:
                choice = input(f"\n{Colors.BRIGHT_CYAN}Choose action for {bot.name}: {Colors.RESET}").strip()

                if choice == '0':
                    break
                elif choice == '1':
                    if bot.is_running:
                        print(f"{Colors.BRIGHT_YELLOW}🛑 Stopping {bot.name}...{Colors.RESET}")
                        bot.stop()
                    else:
                        print(f"{Colors.BRIGHT_GREEN}🚀 Starting {bot.name}...{Colors.RESET}")
                        if bot.start():
                            print(f"{Colors.BRIGHT_GREEN}✅ {bot.name} started successfully{Colors.RESET}")
                        else:
                            print(f"{Colors.BRIGHT_RED}❌ Failed to start {bot.name}{Colors.RESET}")
                elif choice == '2' and bot.is_running:
                    print(f"{Colors.BRIGHT_YELLOW}🔄 Restarting {bot.name}...{Colors.RESET}")
                    if bot.restart():
                        print(f"{Colors.BRIGHT_GREEN}✅ {bot.name} restarted successfully{Colors.RESET}")
                    else:
                        print(f"{Colors.BRIGHT_RED}❌ Failed to restart {bot.name}{Colors.RESET}")
                elif choice == '3':
                    status = bot.get_status()
                    print(f"\n{Colors.BRIGHT_CYAN}📊 Bot Information:{Colors.RESET}")
                    print(f"Name: {bot.color}{status['name']}{Colors.RESET}")
                    print(f"Status: {'🟢 RUNNING' if status['running'] else '🔴 STOPPED'}")
                    print(f"Script: {bot.script_path}")
                    print(f"Env File: {bot.env_file or 'None'}")
                    if status['uptime']:
                        print(f"Uptime: {status['uptime']}")
                    if status['pid']:
                        print(f"PID: {status['pid']}")
                    input(f"\n{Colors.BRIGHT_BLACK}Press Enter to continue...{Colors.RESET}")
                else:
                    print(f"{Colors.BRIGHT_RED}❌ Invalid choice{Colors.RESET}")

            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"{Colors.BRIGHT_RED}❌ Error: {e}{Colors.RESET}")

    def run(self):
        """Run the interactive launcher"""
        print_banner()

        try:
            while True:
                self.show_menu()

                try:
                    choice = input(f"\n{Colors.BRIGHT_CYAN}Enter your choice: {Colors.RESET}").strip()

                    if choice == '0':
                        print(f"{Colors.BRIGHT_YELLOW}👋 Exiting...{Colors.RESET}")
                        break
                    elif choice == '1':
                        print(f"{Colors.BRIGHT_GREEN}🚀 Starting all bots...{Colors.RESET}")
                        self.launcher.start_all()
                    elif choice == '2':
                        print(f"{Colors.BRIGHT_YELLOW}🛑 Stopping all bots...{Colors.RESET}")
                        self.launcher.stop_all()
                    elif choice == '3':
                        print(f"{Colors.BRIGHT_BLUE}🔄 Restarting all bots...{Colors.RESET}")
                        self.launcher.restart_all()
                    elif choice == '4':
                        self.launcher.show_status()
                        input(f"\n{Colors.BRIGHT_BLACK}Press Enter to continue...{Colors.RESET}")
                    elif choice == '5':
                        while True:
                            self.show_individual_menu()
                            try:
                                bot_choice = input(f"\n{Colors.BRIGHT_CYAN}Select bot (0 to go back): {Colors.RESET}").strip()
                                if bot_choice == '0':
                                    break
                                bot_index = int(bot_choice) - 1
                                if 0 <= bot_index < len(self.launcher.bots):
                                    self.handle_individual_bot(bot_index)
                                else:
                                    print(f"{Colors.BRIGHT_RED}❌ Invalid bot number{Colors.RESET}")
                            except ValueError:
                                print(f"{Colors.BRIGHT_RED}❌ Please enter a valid number{Colors.RESET}")
                            except KeyboardInterrupt:
                                break
                    elif choice == '6':
                        self.show_config()
                        input(f"\n{Colors.BRIGHT_BLACK}Press Enter to continue...{Colors.RESET}")
                    else:
                        print(f"{Colors.BRIGHT_RED}❌ Invalid choice. Please try again.{Colors.RESET}")

                except KeyboardInterrupt:
                    print(f"\n{Colors.BRIGHT_YELLOW}🛑 Interrupted{Colors.RESET}")
                    break
                except Exception as e:
                    print(f"{Colors.BRIGHT_RED}❌ Error: {e}{Colors.RESET}")

        finally:
            # Clean shutdown
            if any(bot.is_running for bot in self.launcher.bots):
                print(f"\n{Colors.BRIGHT_YELLOW}🧹 Cleaning up running bots...{Colors.RESET}")
                self.launcher.stop_all()

def main():
    """Main function"""
    launcher = InteractiveLauncher()
    launcher.run()

if __name__ == "__main__":
    main()
